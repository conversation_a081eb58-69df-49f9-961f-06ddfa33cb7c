File Path,Control ID,Control Domain,Control Name,Severity,Line,Description,Remediation,Reference Links,Azure Guidance,Implementation Context,Source,Cross Reference Type,Validation Status
Grafana.deploymentTemplate.json,NS-10,Network Security,Ensure Domain Name System (DNS) security,HIGH,27.0,"The 'autoGeneratedDomainNameLabelScope' property is set to 'TenantReuse', which can increase the risk of DNS subdomain takeover if the resource is deleted and the DNS label is reused by an attacker. This can lead to phishing, credential theft, or redirection of legitimate traffic.","Set 'autoGeneratedDomainNameLabelScope' to 'NoReuse' to prevent DNS label reuse after resource deletion. Regularly audit DNS records and monitor for dangling DNS entries. Reference: ASB NS-10.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/dns/dns-overview) | [NIST DNS Deployment Guide](https://csrc.nist.gov/publications/detail/sp/800-81/2/final) | [Azure Private DNS](https://docs.microsoft.com/azure/dns/private-dns-overview) | [Azure Defender for DNS](https://docs.microsoft.com/azure/security-center/defender-for-dns-introduction) | [Prevent dangling DNS entries](https://docs.microsoft.com/azure/security/fundamentals/subdomain-takeover) | [DNS security best practices](https://docs.microsoft.com/azure/security/fundamentals/network-best-practices#secure-name-resolution) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture)

🔵 Azure Guidance: Use Azure recursive DNS or trusted external DNS servers. Use Azure Private DNS for private zones. Use Azure Defender for DNS for advanced protection against DNS threats including data exfiltration mal...",[Enhanced Implementation Context](https://docs.microsoft.com/azure/dns/dns-overview) | [NIST DNS Deployment Guide](https://csrc.nist.gov/publications/detail/sp/800-81/2/final) | [Azure Private DNS](https://docs.microsoft.com/azure/dns/private-dns-overview) | [Azure Defender for DNS](https://docs.microsoft.com/azure/security-center/defender-for-dns-introduction) | [Prevent dangling DNS entries](https://docs.microsoft.com/azure/security/fundamentals/subdomain-takeover) | [DNS security best practices](https://docs.microsoft.com/azure/security/fundamentals/network-best-practices#secure-name-resolution) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture),Use Azure recursive DNS or trusted external DNS servers. Use Azure Private DNS for private zones. Use Azure Defender for DNS for advanced protection against DNS threats including data exfiltration malware communication and DNS attacks.,"Enhanced Implementation Context:
• Azure DNS overview: https://docs.microsoft.com/azure/dns/dns-overview
• NIST DNS Deployment Guide: https://csrc.nist.gov/publications/detail/sp/800-81/2/final
• Azure Private DNS: https://docs.microsoft.com/azure/dns/private-dns-overview
• Azure Defender for DNS: https://docs.microsoft.com/azure/security-center/defender-for-dns-introduction
• Prevent dangling DNS entries: https://docs.microsoft.com/azure/security/fundamentals/subdomain-takeover
• DNS security best practices: https://docs.microsoft.com/azure/security/fundamentals/network-best-practices#secure-name-resolution
• Security architecture guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture

Compliance Mappings:
• CIS Controls v8: 4.9, 9.2
• NIST SP800-53 r4: SC-20, SC-21
• PCI-DSS v3.2.1: Not specified

Azure Policy Examples:
• Azure Defender for DNS should be enabled
• Configure trusted DNS servers for virtual networks
• Monitor DNS queries for malicious activity
• Implement DNS filtering and threat protection",ai_analysis,,Validated
Grafana.deploymentTemplate.json,NS-2,Network Security,Secure cloud services with network controls,CRITICAL,38.0,"The 'publicNetworkAccess' property is set to 'Enabled' for the Microsoft.Dashboard/grafana resource. This exposes the Grafana instance to the public internet, enabling initial access attack vectors such as brute force, credential stuffing, or exploitation of zero-day vulnerabilities. The blast radius includes potential compromise of monitoring data, lateral movement into the Azure environment, and exposure of sensitive dashboards.","Set 'publicNetworkAccess' to 'Disabled' to restrict access to private endpoints only. Implement Azure Private Link and ensure only trusted VNets/subnets can access the Grafana instance. Review and update network security groups (NSGs) to enforce least privilege access. Reference: ASB NS-2.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/private-link/private-link-overview) | [Private endpoints for Storage](https://docs.microsoft.com/azure/storage/common/storage-private-endpoints) | [SQL private endpoints](https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview) | [Key Vault private endpoints](https://docs.microsoft.com/azure/key-vault/general/private-link-service) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture)

🔵 Azure Guidance: Deploy private endpoints for Azure resources supporting Private Link. Disable public network access where feasible. Use VNet integration for services requiring private access.",[Enhanced Implementation Context](https://docs.microsoft.com/azure/private-link/private-link-overview) | [Private endpoints for Storage](https://docs.microsoft.com/azure/storage/common/storage-private-endpoints) | [SQL private endpoints](https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview) | [Key Vault private endpoints](https://docs.microsoft.com/azure/key-vault/general/private-link-service) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture),Deploy private endpoints for Azure resources supporting Private Link. Disable public network access where feasible. Use VNet integration for services requiring private access.,"Enhanced Implementation Context:
• Private Link overview: https://docs.microsoft.com/azure/private-link/private-link-overview
• Private endpoints for Storage: https://docs.microsoft.com/azure/storage/common/storage-private-endpoints
• SQL private endpoints: https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview
• Key Vault private endpoints: https://docs.microsoft.com/azure/key-vault/general/private-link-service
• Security architecture guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture

Compliance Mappings:
• CIS Controls v8: 3.12, 4.4
• NIST SP800-53 r4: AC-4, SC-2, SC-7
• PCI-DSS v3.2.1: 1.1, 1.2, 1.3

Azure Policy Examples:
• Private endpoint should be enabled for PostgreSQL/MySQL/MariaDB servers
• Storage accounts should use private link
• Azure SQL Database should disable public network access
• Cognitive Services accounts should restrict network access
• Container registries should use private link",ai_analysis,,Validated
Grafana.deploymentTemplate.json,NS-3,Network Security,Deploy firewall at the edge of enterprise network,CRITICAL,38.0,"With 'publicNetworkAccess' set to 'Enabled', there is no evidence of Azure Firewall or equivalent network filtering in place for the Grafana instance. This allows unrestricted inbound and outbound traffic, increasing the risk of network compromise, lateral movement, and data exfiltration if the service is exploited.","Deploy Azure Firewall or a similar network security appliance to restrict and monitor traffic to and from the Grafana instance. Define user-defined routes (UDRs) to ensure all traffic passes through the firewall. Reference: ASB NS-3.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/firewall/tutorial-firewall-deploy-portal) | [Virtual network traffic routing](https://docs.microsoft.com/azure/virtual-network/virtual-networks-udr-overview) | [Azure Firewall Manager](https://docs.microsoft.com/azure/firewall-manager/overview) | [Hub-spoke topology](https://docs.microsoft.com/azure/architecture/reference-architectures/hybrid-networking/hub-spoke) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture)

🔵 Azure Guidance: Use Azure Firewall for stateful application layer traffic restriction and central management. Create user-defined routes (UDR) for complex topologies. Block known bad IPs and high-risk protocols.",[Enhanced Implementation Context](https://docs.microsoft.com/azure/firewall/tutorial-firewall-deploy-portal) | [Virtual network traffic routing](https://docs.microsoft.com/azure/virtual-network/virtual-networks-udr-overview) | [Azure Firewall Manager](https://docs.microsoft.com/azure/firewall-manager/overview) | [Hub-spoke topology](https://docs.microsoft.com/azure/architecture/reference-architectures/hybrid-networking/hub-spoke) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture),Use Azure Firewall for stateful application layer traffic restriction and central management. Create user-defined routes (UDR) for complex topologies. Block known bad IPs and high-risk protocols.,"Enhanced Implementation Context:
• Azure Firewall deployment: https://docs.microsoft.com/azure/firewall/tutorial-firewall-deploy-portal
• Virtual network traffic routing: https://docs.microsoft.com/azure/virtual-network/virtual-networks-udr-overview
• Azure Firewall Manager: https://docs.microsoft.com/azure/firewall-manager/overview
• Hub-spoke topology: https://docs.microsoft.com/azure/architecture/reference-architectures/hybrid-networking/hub-spoke
• Security architecture guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture

Compliance Mappings:
• CIS Controls v8: 4.4, 4.8, 13.10
• NIST SP800-53 r4: AC-4, SC-7, CM-7
• PCI-DSS v3.2.1: 1.1, 1.2, 1.3

Azure Policy Examples:
• Management ports should be closed on your virtual machines
• Management ports of virtual machines should be protected with just-in-time network access control
• IP Forwarding on your virtual machine should be disabled
• All Internet traffic should be routed via your deployed Azure Firewall",ai_analysis,,Validated
Grafana.deploymentTemplate.json,NS-1,Network Security,Establish network segmentation boundaries,CRITICAL,38.0,"The configuration enables public network access without any segmentation or network security group (NSG) controls, violating segmentation best practices. This allows direct access from untrusted networks, increasing the risk of initial access and lateral movement within the Azure environment.","Associate the Grafana resource with a dedicated subnet protected by a restrictive NSG. Apply a deny-by-default policy and only allow required management or application ports from trusted sources. Reference: ASB NS-1.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/virtual-network/concepts-and-best-practices) | [Subnet management](https://docs.microsoft.com/azure/virtual-network/virtual-network-manage-subnet) | [NSG security rules](https://docs.microsoft.com/azure/virtual-network/tutorial-filter-network-traffic) | [Application security groups](https://docs.microsoft.com/azure/virtual-network/network-security-groups-overview#application-security-groups) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture)

🔵 Azure Guidance: Create virtual networks (VNets) as fundamental segmentation. Use network security groups (NSGs) and application security groups (ASGs) for traffic control. Implement deny-by-default approach.",[Enhanced Implementation Context](https://docs.microsoft.com/azure/virtual-network/concepts-and-best-practices) | [Subnet management](https://docs.microsoft.com/azure/virtual-network/virtual-network-manage-subnet) | [NSG security rules](https://docs.microsoft.com/azure/virtual-network/tutorial-filter-network-traffic) | [Application security groups](https://docs.microsoft.com/azure/virtual-network/network-security-groups-overview#application-security-groups) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture),Create virtual networks (VNets) as fundamental segmentation. Use network security groups (NSGs) and application security groups (ASGs) for traffic control. Implement deny-by-default approach.,"Enhanced Implementation Context:
• Virtual network concepts: https://docs.microsoft.com/azure/virtual-network/concepts-and-best-practices
• Subnet management: https://docs.microsoft.com/azure/virtual-network/virtual-network-manage-subnet
• NSG security rules: https://docs.microsoft.com/azure/virtual-network/tutorial-filter-network-traffic
• Application security groups: https://docs.microsoft.com/azure/virtual-network/network-security-groups-overview#application-security-groups
• Security architecture guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture

Compliance Mappings:
• CIS Controls v8: 3.12, 13.4, 4.4
• NIST SP800-53 r4: AC-4, SC-2, SC-7
• PCI-DSS v3.2.1: 1.1, 1.2, 1.3

Azure Policy Examples:
• Adaptive network hardening recommendations should be applied on internet facing virtual machines
• All network ports should be restricted on network security groups associated to your virtual machine
• Subnets should be associated with a Network Security Group",ai_analysis,,Validated
Grafana.deploymentTemplate.json,NS-5,Network Security,Deploy DDoS protection,HIGH,38.0,"Publicly accessible Grafana instances are common targets for DDoS attacks. The template does not indicate that Azure DDoS Protection Standard is enabled for the associated virtual network, increasing the risk of service disruption and potential exploitation during an attack.","Enable Azure DDoS Protection Standard on the virtual network hosting the Grafana instance. Monitor DDoS metrics and configure alerts for attack detection and response. Reference: ASB NS-5.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/virtual-network/manage-ddos-protection) | [DDoS response strategy](https://docs.microsoft.com/azure/ddos-protection/ddos-response-strategy) | [DDoS monitoring and alerting](https://docs.microsoft.com/azure/ddos-protection/telemetry-monitoring-alerting) | [DDoS best practices](https://docs.microsoft.com/azure/security/fundamentals/ddos-best-practices) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture)

🔵 Azure Guidance: Enable DDoS Standard protection plan on your VNet to protect resources exposed to public networks. Configure DDoS policies and monitoring.",[Enhanced Implementation Context](https://docs.microsoft.com/azure/virtual-network/manage-ddos-protection) | [DDoS response strategy](https://docs.microsoft.com/azure/ddos-protection/ddos-response-strategy) | [DDoS monitoring and alerting](https://docs.microsoft.com/azure/ddos-protection/telemetry-monitoring-alerting) | [DDoS best practices](https://docs.microsoft.com/azure/security/fundamentals/ddos-best-practices) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture),Enable DDoS Standard protection plan on your VNet to protect resources exposed to public networks. Configure DDoS policies and monitoring.,"Enhanced Implementation Context:
• Azure DDoS Protection Standard: https://docs.microsoft.com/azure/virtual-network/manage-ddos-protection
• DDoS response strategy: https://docs.microsoft.com/azure/ddos-protection/ddos-response-strategy
• DDoS monitoring and alerting: https://docs.microsoft.com/azure/ddos-protection/telemetry-monitoring-alerting
• DDoS best practices: https://docs.microsoft.com/azure/security/fundamentals/ddos-best-practices
• Security architecture guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture

Compliance Mappings:
• CIS Controls v8: 13.10
• NIST SP800-53 r4: SC-5, SC-7
• PCI-DSS v3.2.1: 1.1, 1.2, 1.3, 6.6

Azure Policy Examples:
• Azure DDoS Protection Standard should be enabled
• Monitor DDoS attack metrics and configure alerts
• Implement DDoS response procedures",ai_analysis,,Validated
KustoScripts.template.json,DP-2,Data Protection,Monitor anomalies and threats targeting sensitive data,CRITICAL,55.0,"""continueOnErrors"": true is set for Kusto script deployment. This setting allows scripts to proceed even if errors occur, potentially masking failed or partial deployments. Attackers could exploit this to introduce malicious or incomplete scripts without detection, increasing the risk of unauthorized data access or exfiltration. The blast radius includes undetected data manipulation or exfiltration from the Kusto database.","Set ""continueOnErrors"" to false for all Kusto script deployments to ensure that any error halts the deployment process. Implement monitoring and alerting for failed script executions. Reference: ASB DP-2 (Monitor for anomalies around sensitive data such as unauthorized transfer to locations outside enterprise visibility and control).

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/azure-sql/database/azure-defender-for-sql) | [Azure Defender for Storage](https://docs.microsoft.com/azure/storage/common/storage-advanced-threat-protection?tabs=azure-security-center) | [Azure Purview data insights](https://docs.microsoft.com/azure/purview/concept-insights) | [Microsoft 365 DLP](https://docs.microsoft.com/microsoft-365/compliance/dlp-learn-about-dlp) | [Azure Information Protection monitoring](https://docs.microsoft.com/azure/information-protection/reports-aip)

🔵 Azure Guidance: Use Azure Information Protection to monitor classified and labeled data. Use Azure Defender for Storage SQL and Cosmos DB to alert on anomalous transfers. Consider Microsoft 365 DLP or host-based DLP ...",[Enhanced Implementation Context](https://docs.microsoft.com/azure/azure-sql/database/azure-defender-for-sql) | [Azure Defender for Storage](https://docs.microsoft.com/azure/storage/common/storage-advanced-threat-protection?tabs=azure-security-center) | [Azure Purview data insights](https://docs.microsoft.com/azure/purview/concept-insights) | [Microsoft 365 DLP](https://docs.microsoft.com/microsoft-365/compliance/dlp-learn-about-dlp) | [Azure Information Protection monitoring](https://docs.microsoft.com/azure/information-protection/reports-aip),Use Azure Information Protection to monitor classified and labeled data. Use Azure Defender for Storage SQL and Cosmos DB to alert on anomalous transfers. Consider Microsoft 365 DLP or host-based DLP solutions for compliance requirements.,"Enhanced Implementation Context:
• Azure Defender for SQL: https://docs.microsoft.com/azure/azure-sql/database/azure-defender-for-sql
• Azure Defender for Storage: https://docs.microsoft.com/azure/storage/common/storage-advanced-threat-protection?tabs=azure-security-center
• Azure Purview data insights: https://docs.microsoft.com/azure/purview/concept-insights
• Microsoft 365 DLP: https://docs.microsoft.com/microsoft-365/compliance/dlp-learn-about-dlp
• Azure Information Protection monitoring: https://docs.microsoft.com/azure/information-protection/reports-aip

Compliance Mappings:
• CIS Controls v8: 3.13
• NIST SP800-53 r4: AC-4, SI-4
• PCI-DSS v3.2.1: A3.2

Azure Policy Examples:
• Azure Defender for open-source relational databases should be enabled
• Azure Defender for Storage should be enabled
• Azure Defender for SQL servers on machines should be enabled
• Azure Defender for Azure SQL Database servers should be enabled
• Azure Defender for SQL should be enabled for unprotected SQL Managed Instances",ai_analysis,,Validated
KustoScripts.template.json,DP-2,Data Protection,Monitor anomalies and threats targeting sensitive data,CRITICAL,105.0,"The property 'continueOnErrors' is set to true for the Kusto script resource. This configuration allows the deployment to proceed even if the script fails, which can mask unauthorized or anomalous data access or manipulation attempts. Attackers could exploit this by introducing malicious or incomplete scripts that bypass detection, increasing the risk of data exfiltration or integrity compromise. The blast radius includes undetected data loss, corruption, or unauthorized access in the Kusto database.","Set 'continueOnErrors' to false to ensure that any script execution failure halts the deployment and triggers alerting. Implement monitoring and alerting for all script execution failures using Azure Defender for SQL and Storage. Regularly review deployment logs for anomalies. Reference: Azure Security Benchmark DP-2.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/azure-sql/database/azure-defender-for-sql) | [Azure Defender for Storage](https://docs.microsoft.com/azure/storage/common/storage-advanced-threat-protection?tabs=azure-security-center) | [Azure Purview data insights](https://docs.microsoft.com/azure/purview/concept-insights) | [Microsoft 365 DLP](https://docs.microsoft.com/microsoft-365/compliance/dlp-learn-about-dlp) | [Azure Information Protection monitoring](https://docs.microsoft.com/azure/information-protection/reports-aip)

🔵 Azure Guidance: Use Azure Information Protection to monitor classified and labeled data. Use Azure Defender for Storage SQL and Cosmos DB to alert on anomalous transfers. Consider Microsoft 365 DLP or host-based DLP ...",[Enhanced Implementation Context](https://docs.microsoft.com/azure/azure-sql/database/azure-defender-for-sql) | [Azure Defender for Storage](https://docs.microsoft.com/azure/storage/common/storage-advanced-threat-protection?tabs=azure-security-center) | [Azure Purview data insights](https://docs.microsoft.com/azure/purview/concept-insights) | [Microsoft 365 DLP](https://docs.microsoft.com/microsoft-365/compliance/dlp-learn-about-dlp) | [Azure Information Protection monitoring](https://docs.microsoft.com/azure/information-protection/reports-aip),Use Azure Information Protection to monitor classified and labeled data. Use Azure Defender for Storage SQL and Cosmos DB to alert on anomalous transfers. Consider Microsoft 365 DLP or host-based DLP solutions for compliance requirements.,"Enhanced Implementation Context:
• Azure Defender for SQL: https://docs.microsoft.com/azure/azure-sql/database/azure-defender-for-sql
• Azure Defender for Storage: https://docs.microsoft.com/azure/storage/common/storage-advanced-threat-protection?tabs=azure-security-center
• Azure Purview data insights: https://docs.microsoft.com/azure/purview/concept-insights
• Microsoft 365 DLP: https://docs.microsoft.com/microsoft-365/compliance/dlp-learn-about-dlp
• Azure Information Protection monitoring: https://docs.microsoft.com/azure/information-protection/reports-aip

Compliance Mappings:
• CIS Controls v8: 3.13
• NIST SP800-53 r4: AC-4, SI-4
• PCI-DSS v3.2.1: A3.2

Azure Policy Examples:
• Azure Defender for open-source relational databases should be enabled
• Azure Defender for Storage should be enabled
• Azure Defender for SQL servers on machines should be enabled
• Azure Defender for Azure SQL Database servers should be enabled
• Azure Defender for SQL should be enabled for unprotected SQL Managed Instances",ai_analysis,,Validated
KustoScripts.template.json,DP-2,Data Protection,Monitor anomalies and threats targeting sensitive data,CRITICAL,176.0,"The 'continueOnErrors' property is set to true for the Microsoft.Kusto/clusters/databases/scripts resource. This setting allows the deployment to proceed even if the script fails, which can result in incomplete or inconsistent data processing. Attackers could exploit this by causing intentional script failures to bypass data validation or monitoring, leading to potential data exfiltration or unauthorized data manipulation. The blast radius includes the risk of undetected data loss or corruption in production environments.","Set 'continueOnErrors' to false to ensure that any script execution failure halts the deployment and triggers alerts. Implement monitoring and alerting for all script failures to ensure immediate response and investigation. Reference: Azure Security Benchmark DP-2.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/azure-sql/database/azure-defender-for-sql) | [Azure Defender for Storage](https://docs.microsoft.com/azure/storage/common/storage-advanced-threat-protection?tabs=azure-security-center) | [Azure Purview data insights](https://docs.microsoft.com/azure/purview/concept-insights) | [Microsoft 365 DLP](https://docs.microsoft.com/microsoft-365/compliance/dlp-learn-about-dlp) | [Azure Information Protection monitoring](https://docs.microsoft.com/azure/information-protection/reports-aip)

🔵 Azure Guidance: Use Azure Information Protection to monitor classified and labeled data. Use Azure Defender for Storage SQL and Cosmos DB to alert on anomalous transfers. Consider Microsoft 365 DLP or host-based DLP ...",[Enhanced Implementation Context](https://docs.microsoft.com/azure/azure-sql/database/azure-defender-for-sql) | [Azure Defender for Storage](https://docs.microsoft.com/azure/storage/common/storage-advanced-threat-protection?tabs=azure-security-center) | [Azure Purview data insights](https://docs.microsoft.com/azure/purview/concept-insights) | [Microsoft 365 DLP](https://docs.microsoft.com/microsoft-365/compliance/dlp-learn-about-dlp) | [Azure Information Protection monitoring](https://docs.microsoft.com/azure/information-protection/reports-aip),Use Azure Information Protection to monitor classified and labeled data. Use Azure Defender for Storage SQL and Cosmos DB to alert on anomalous transfers. Consider Microsoft 365 DLP or host-based DLP solutions for compliance requirements.,"Enhanced Implementation Context:
• Azure Defender for SQL: https://docs.microsoft.com/azure/azure-sql/database/azure-defender-for-sql
• Azure Defender for Storage: https://docs.microsoft.com/azure/storage/common/storage-advanced-threat-protection?tabs=azure-security-center
• Azure Purview data insights: https://docs.microsoft.com/azure/purview/concept-insights
• Microsoft 365 DLP: https://docs.microsoft.com/microsoft-365/compliance/dlp-learn-about-dlp
• Azure Information Protection monitoring: https://docs.microsoft.com/azure/information-protection/reports-aip

Compliance Mappings:
• CIS Controls v8: 3.13
• NIST SP800-53 r4: AC-4, SI-4
• PCI-DSS v3.2.1: A3.2

Azure Policy Examples:
• Azure Defender for open-source relational databases should be enabled
• Azure Defender for Storage should be enabled
• Azure Defender for SQL servers on machines should be enabled
• Azure Defender for Azure SQL Database servers should be enabled
• Azure Defender for SQL should be enabled for unprotected SQL Managed Instances",ai_analysis,,Validated
KustoScripts.template.json,DP-2,Data Protection,Monitor anomalies and threats targeting sensitive data,CRITICAL,219.0,"The function defined at line 219 does not include any monitoring or alerting for anomalous access or data exfiltration activities. If an attacker abuses this function, they could extract large volumes of sensitive data (moniker values by region) without detection, enabling data exfiltration or reconnaissance.","Enable Azure Defender for SQL, Storage, and Kusto resources to monitor for anomalous data access and exfiltration. Configure alerts for unusual query patterns or large data exports from the function. Reference: DP-2.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/azure-sql/database/azure-defender-for-sql) | [Azure Defender for Storage](https://docs.microsoft.com/azure/storage/common/storage-advanced-threat-protection?tabs=azure-security-center) | [Azure Purview data insights](https://docs.microsoft.com/azure/purview/concept-insights) | [Microsoft 365 DLP](https://docs.microsoft.com/microsoft-365/compliance/dlp-learn-about-dlp) | [Azure Information Protection monitoring](https://docs.microsoft.com/azure/information-protection/reports-aip)

🔵 Azure Guidance: Use Azure Information Protection to monitor classified and labeled data. Use Azure Defender for Storage SQL and Cosmos DB to alert on anomalous transfers. Consider Microsoft 365 DLP or host-based DLP ...",[Enhanced Implementation Context](https://docs.microsoft.com/azure/azure-sql/database/azure-defender-for-sql) | [Azure Defender for Storage](https://docs.microsoft.com/azure/storage/common/storage-advanced-threat-protection?tabs=azure-security-center) | [Azure Purview data insights](https://docs.microsoft.com/azure/purview/concept-insights) | [Microsoft 365 DLP](https://docs.microsoft.com/microsoft-365/compliance/dlp-learn-about-dlp) | [Azure Information Protection monitoring](https://docs.microsoft.com/azure/information-protection/reports-aip),Use Azure Information Protection to monitor classified and labeled data. Use Azure Defender for Storage SQL and Cosmos DB to alert on anomalous transfers. Consider Microsoft 365 DLP or host-based DLP solutions for compliance requirements.,"Enhanced Implementation Context:
• Azure Defender for SQL: https://docs.microsoft.com/azure/azure-sql/database/azure-defender-for-sql
• Azure Defender for Storage: https://docs.microsoft.com/azure/storage/common/storage-advanced-threat-protection?tabs=azure-security-center
• Azure Purview data insights: https://docs.microsoft.com/azure/purview/concept-insights
• Microsoft 365 DLP: https://docs.microsoft.com/microsoft-365/compliance/dlp-learn-about-dlp
• Azure Information Protection monitoring: https://docs.microsoft.com/azure/information-protection/reports-aip

Compliance Mappings:
• CIS Controls v8: 3.13
• NIST SP800-53 r4: AC-4, SI-4
• PCI-DSS v3.2.1: A3.2

Azure Policy Examples:
• Azure Defender for open-source relational databases should be enabled
• Azure Defender for Storage should be enabled
• Azure Defender for SQL servers on machines should be enabled
• Azure Defender for Azure SQL Database servers should be enabled
• Azure Defender for SQL should be enabled for unprotected SQL Managed Instances",ai_analysis,,Validated
KustoScripts.template.json,DP-1,Data Protection,Discover classify and label sensitive data,CRITICAL,219.0,"The script at line 219 references a data source and creates a function that processes potentially sensitive data (monikers, regions, environment scopes) without any evidence of data classification, labeling, or inventory. Attackers who gain access to this function or its outputs could enumerate or exfiltrate sensitive business or infrastructure metadata, increasing the blast radius of a compromise.","Implement Azure Purview or Azure Information Protection to classify and label all sensitive data processed by this function. Ensure that all Kusto tables and outputs referenced in the script are inventoried and classified according to organizational data protection policies. Reference: DP-1.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/cloud-adoption-framework/govern/policy-compliance/data-classification) | [Azure Purview sensitivity labels](https://docs.microsoft.com/azure/purview/create-sensitivity-label) | [Azure Information Protection](https://docs.microsoft.com/azure/information-protection/what-is-information-protection) | [Azure SQL Data Discovery](https://docs.microsoft.com/azure/sql-database/sql-database-data-discovery-and-classification) | [Azure Purview data sources](https://docs.microsoft.com/azure/purview/purview-connector-overview#purview-data-sources)

🔵 Azure Guidance: Use Azure Purview Azure Information Protection and Azure SQL Data Discovery and Classification to centrally scan classify and label sensitive data across Azure on-premises Microsoft 365 and other loca...",[Enhanced Implementation Context](https://docs.microsoft.com/azure/cloud-adoption-framework/govern/policy-compliance/data-classification) | [Azure Purview sensitivity labels](https://docs.microsoft.com/azure/purview/create-sensitivity-label) | [Azure Information Protection](https://docs.microsoft.com/azure/information-protection/what-is-information-protection) | [Azure SQL Data Discovery](https://docs.microsoft.com/azure/sql-database/sql-database-data-discovery-and-classification) | [Azure Purview data sources](https://docs.microsoft.com/azure/purview/purview-connector-overview#purview-data-sources),Use Azure Purview Azure Information Protection and Azure SQL Data Discovery and Classification to centrally scan classify and label sensitive data across Azure on-premises Microsoft 365 and other locations.,"Enhanced Implementation Context:
• Data classification overview: https://docs.microsoft.com/azure/cloud-adoption-framework/govern/policy-compliance/data-classification
• Azure Purview sensitivity labels: https://docs.microsoft.com/azure/purview/create-sensitivity-label
• Azure Information Protection: https://docs.microsoft.com/azure/information-protection/what-is-information-protection
• Azure SQL Data Discovery: https://docs.microsoft.com/azure/sql-database/sql-database-data-discovery-and-classification
• Azure Purview data sources: https://docs.microsoft.com/azure/purview/purview-connector-overview#purview-data-sources

Compliance Mappings:
• CIS Controls v8: 3.2, 3.7, 3.13
• NIST SP800-53 r4: RA-2, SC-28
• PCI-DSS v3.2.1: A3.2

Azure Policy Examples:
• Sensitive data in your SQL databases should be classified
• Implement data discovery and classification across all data stores
• Deploy Azure Purview for enterprise data governance
• Configure sensitivity labels for all data assets",ai_analysis,,Validated
KustoScripts.template.json,DP-2,Data Protection,Monitor anomalies and threats targeting sensitive data,CRITICAL,291.0,"The configuration sets 'continueOnErrors' to true, which can suppress error reporting and allow data processing to continue even in the presence of failures. This can enable attackers to exploit undetected data exfiltration or unauthorized data manipulation, as anomalous or unauthorized activities may not trigger alerts or halt processing. The blast radius includes potential undetected data leaks or corruption in downstream systems.","Set 'continueOnErrors' to false to ensure that any errors in data processing or transfer are immediately surfaced and remediated. Implement robust monitoring and alerting for all data operations. Reference: Azure Security Benchmark DP-2 (Monitor for anomalies around sensitive data such as unauthorized transfer to locations outside enterprise visibility and control).

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/azure-sql/database/azure-defender-for-sql) | [Azure Defender for Storage](https://docs.microsoft.com/azure/storage/common/storage-advanced-threat-protection?tabs=azure-security-center) | [Azure Purview data insights](https://docs.microsoft.com/azure/purview/concept-insights) | [Microsoft 365 DLP](https://docs.microsoft.com/microsoft-365/compliance/dlp-learn-about-dlp) | [Azure Information Protection monitoring](https://docs.microsoft.com/azure/information-protection/reports-aip)

🔵 Azure Guidance: Use Azure Information Protection to monitor classified and labeled data. Use Azure Defender for Storage SQL and Cosmos DB to alert on anomalous transfers. Consider Microsoft 365 DLP or host-based DLP ...",[Enhanced Implementation Context](https://docs.microsoft.com/azure/azure-sql/database/azure-defender-for-sql) | [Azure Defender for Storage](https://docs.microsoft.com/azure/storage/common/storage-advanced-threat-protection?tabs=azure-security-center) | [Azure Purview data insights](https://docs.microsoft.com/azure/purview/concept-insights) | [Microsoft 365 DLP](https://docs.microsoft.com/microsoft-365/compliance/dlp-learn-about-dlp) | [Azure Information Protection monitoring](https://docs.microsoft.com/azure/information-protection/reports-aip),Use Azure Information Protection to monitor classified and labeled data. Use Azure Defender for Storage SQL and Cosmos DB to alert on anomalous transfers. Consider Microsoft 365 DLP or host-based DLP solutions for compliance requirements.,"Enhanced Implementation Context:
• Azure Defender for SQL: https://docs.microsoft.com/azure/azure-sql/database/azure-defender-for-sql
• Azure Defender for Storage: https://docs.microsoft.com/azure/storage/common/storage-advanced-threat-protection?tabs=azure-security-center
• Azure Purview data insights: https://docs.microsoft.com/azure/purview/concept-insights
• Microsoft 365 DLP: https://docs.microsoft.com/microsoft-365/compliance/dlp-learn-about-dlp
• Azure Information Protection monitoring: https://docs.microsoft.com/azure/information-protection/reports-aip

Compliance Mappings:
• CIS Controls v8: 3.13
• NIST SP800-53 r4: AC-4, SI-4
• PCI-DSS v3.2.1: A3.2

Azure Policy Examples:
• Azure Defender for open-source relational databases should be enabled
• Azure Defender for Storage should be enabled
• Azure Defender for SQL servers on machines should be enabled
• Azure Defender for Azure SQL Database servers should be enabled
• Azure Defender for SQL should be enabled for unprotected SQL Managed Instances",ai_analysis,,Validated
KustoScripts.template.json,DP-2,Data Protection,Monitor anomalies and threats targeting sensitive data,CRITICAL,319.0,"The 'continueOnErrors' property is set to true, which allows the Logic App to proceed even if errors occur during script execution. This can enable attackers to suppress error signals from failed or malicious operations, potentially masking unauthorized data access or exfiltration attempts. The blast radius includes undetected data exfiltration or manipulation, as monitoring and alerting systems may not be triggered by suppressed errors.","Set 'continueOnErrors' to false to ensure that any error in the script execution halts the process and triggers alerting and monitoring. Implement robust error handling and monitoring to detect and respond to anomalous activities. Reference: Azure Security Benchmark DP-2.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/azure-sql/database/azure-defender-for-sql) | [Azure Defender for Storage](https://docs.microsoft.com/azure/storage/common/storage-advanced-threat-protection?tabs=azure-security-center) | [Azure Purview data insights](https://docs.microsoft.com/azure/purview/concept-insights) | [Microsoft 365 DLP](https://docs.microsoft.com/microsoft-365/compliance/dlp-learn-about-dlp) | [Azure Information Protection monitoring](https://docs.microsoft.com/azure/information-protection/reports-aip)

🔵 Azure Guidance: Use Azure Information Protection to monitor classified and labeled data. Use Azure Defender for Storage SQL and Cosmos DB to alert on anomalous transfers. Consider Microsoft 365 DLP or host-based DLP ...",[Enhanced Implementation Context](https://docs.microsoft.com/azure/azure-sql/database/azure-defender-for-sql) | [Azure Defender for Storage](https://docs.microsoft.com/azure/storage/common/storage-advanced-threat-protection?tabs=azure-security-center) | [Azure Purview data insights](https://docs.microsoft.com/azure/purview/concept-insights) | [Microsoft 365 DLP](https://docs.microsoft.com/microsoft-365/compliance/dlp-learn-about-dlp) | [Azure Information Protection monitoring](https://docs.microsoft.com/azure/information-protection/reports-aip),Use Azure Information Protection to monitor classified and labeled data. Use Azure Defender for Storage SQL and Cosmos DB to alert on anomalous transfers. Consider Microsoft 365 DLP or host-based DLP solutions for compliance requirements.,"Enhanced Implementation Context:
• Azure Defender for SQL: https://docs.microsoft.com/azure/azure-sql/database/azure-defender-for-sql
• Azure Defender for Storage: https://docs.microsoft.com/azure/storage/common/storage-advanced-threat-protection?tabs=azure-security-center
• Azure Purview data insights: https://docs.microsoft.com/azure/purview/concept-insights
• Microsoft 365 DLP: https://docs.microsoft.com/microsoft-365/compliance/dlp-learn-about-dlp
• Azure Information Protection monitoring: https://docs.microsoft.com/azure/information-protection/reports-aip

Compliance Mappings:
• CIS Controls v8: 3.13
• NIST SP800-53 r4: AC-4, SI-4
• PCI-DSS v3.2.1: A3.2

Azure Policy Examples:
• Azure Defender for open-source relational databases should be enabled
• Azure Defender for Storage should be enabled
• Azure Defender for SQL servers on machines should be enabled
• Azure Defender for Azure SQL Database servers should be enabled
• Azure Defender for SQL should be enabled for unprotected SQL Managed Instances",ai_analysis,,Validated
roleAssignment.deploymentTemplate.json,IM-6,Identity Management,Use strong authentication controls,CRITICAL,5.0,"The 'principalId' property is assigned the Owner role without any indication of enforced strong authentication (such as MFA) or conditional access. This enables an attacker who compromises the principal's credentials to immediately gain full administrative access, facilitating privilege escalation and lateral movement. The blast radius is total subscription compromise.","Require multi-factor authentication (MFA) for all accounts assigned the Owner role. Enforce strong authentication policies and block legacy authentication methods. Use Conditional Access policies to restrict access to trusted locations and devices. Reference: Azure Security Benchmark v3.0 IM-6.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/active-directory/authentication/howto-mfa-getstarted) | [Passwordless authentication options](https://docs.microsoft.com/azure/active-directory/authentication/concept-authentication-passwordless) | [Azure AD password policies](https://docs.microsoft.com/azure/active-directory/authentication/concept-sspr-policy#password-policies-that-only-apply-to-cloud-user-accounts) | [Azure AD Password Protection](https://docs.microsoft.com/azure/active-directory/authentication/concept-password-ban-bad) | [Block legacy authentication](https://docs.microsoft.com/azure/active-directory/conditional-access/block-legacy-authentication)

🔵 Azure Guidance: Use Azure AD passwordless authentication as default method (Windows Hello Microsoft Authenticator FIDO2 Keys). Enable Azure MFA for all users with conditional access policies. Block legacy authenticat...",[Enhanced Implementation Context](https://docs.microsoft.com/azure/active-directory/authentication/howto-mfa-getstarted) | [Passwordless authentication options](https://docs.microsoft.com/azure/active-directory/authentication/concept-authentication-passwordless) | [Azure AD password policies](https://docs.microsoft.com/azure/active-directory/authentication/concept-sspr-policy#password-policies-that-only-apply-to-cloud-user-accounts) | [Azure AD Password Protection](https://docs.microsoft.com/azure/active-directory/authentication/concept-password-ban-bad) | [Block legacy authentication](https://docs.microsoft.com/azure/active-directory/conditional-access/block-legacy-authentication),Use Azure AD passwordless authentication as default method (Windows Hello Microsoft Authenticator FIDO2 Keys). Enable Azure MFA for all users with conditional access policies. Block legacy authentication.,"Enhanced Implementation Context:
• Azure MFA deployment: https://docs.microsoft.com/azure/active-directory/authentication/howto-mfa-getstarted
• Passwordless authentication options: https://docs.microsoft.com/azure/active-directory/authentication/concept-authentication-passwordless
• Azure AD password policies: https://docs.microsoft.com/azure/active-directory/authentication/concept-sspr-policy#password-policies-that-only-apply-to-cloud-user-accounts
• Azure AD Password Protection: https://docs.microsoft.com/azure/active-directory/authentication/concept-password-ban-bad
• Block legacy authentication: https://docs.microsoft.com/azure/active-directory/conditional-access/block-legacy-authentication

Compliance Mappings:
• CIS Controls v8: 6.3, 6.4
• NIST SP800-53 r4: AC-2, AC-3, IA-2, IA-5, IA-8
• PCI-DSS v3.2.1: 7.2, 8.2, 8.3, 8.4

Azure Policy Examples:
• Authentication to Linux machines should require SSH keys
• MFA should be enabled on accounts with write permissions on your subscription
• MFA should be enabled on accounts with owner permissions on your subscription
• MFA should be enabled on accounts with read permissions on your subscription",ai_analysis,,Validated
roleAssignment.deploymentTemplate.json,IM-2,Identity Management,Protect identity and authentication systems,CRITICAL,11.0,"The 'builtInRoleType' parameter is set to 'Owner' by default, and line 26 assigns the 'Owner' roleDefinitionId (8e3af657-a8ff-443c-a75c-2fe8c4bcb635) to the specified principal. Assigning the Owner role grants full administrative privileges over the subscription, enabling initial access, privilege escalation, and lateral movement if the principal is compromised. The blast radius includes full control over all Azure resources in the subscription, including the ability to delete, exfiltrate, or modify resources and access sensitive data.","Restrict the use of the 'Owner' role. Assign the least privileged built-in role necessary for the principal's function (e.g., 'Contributor' or custom roles with limited permissions). Implement Privileged Identity Management (PIM) to require just-in-time elevation and multi-factor authentication for privileged roles. Monitor and audit all role assignments for high-privilege roles. Reference: Azure Security Benchmark v3.0 IM-2.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/active-directory/fundamentals/identity-secure-score) | [Active Directory security best practices](https://docs.microsoft.com/windows-server/identity/ad-ds/plan/security-best-practices/best-practices-for-securing-active-directory) | [Azure AD security baseline](https://docs.microsoft.com/security/benchmark/azure/baselines/aad-security-baseline) | [Privileged Identity Management](https://docs.microsoft.com/azure/active-directory/privileged-identity-management/pim-configure) | [Identity and key management guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys)

🔵 Azure Guidance: Use Azure AD security baseline and Identity Secure Score to evaluate security posture. Implement limited administrative roles user risk policies MFA for administrative roles and block legacy authentic...",[Enhanced Implementation Context](https://docs.microsoft.com/azure/active-directory/fundamentals/identity-secure-score) | [Active Directory security best practices](https://docs.microsoft.com/windows-server/identity/ad-ds/plan/security-best-practices/best-practices-for-securing-active-directory) | [Azure AD security baseline](https://docs.microsoft.com/security/benchmark/azure/baselines/aad-security-baseline) | [Privileged Identity Management](https://docs.microsoft.com/azure/active-directory/privileged-identity-management/pim-configure) | [Identity and key management guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys),Use Azure AD security baseline and Identity Secure Score to evaluate security posture. Implement limited administrative roles user risk policies MFA for administrative roles and block legacy authentication.,"Enhanced Implementation Context:
• Azure AD Identity Secure Score: https://docs.microsoft.com/azure/active-directory/fundamentals/identity-secure-score
• Active Directory security best practices: https://docs.microsoft.com/windows-server/identity/ad-ds/plan/security-best-practices/best-practices-for-securing-active-directory
• Azure AD security baseline: https://docs.microsoft.com/security/benchmark/azure/baselines/aad-security-baseline
• Privileged Identity Management: https://docs.microsoft.com/azure/active-directory/privileged-identity-management/pim-configure
• Identity and key management guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys

Compliance Mappings:
• CIS Controls v8: 5.4, 6.5
• NIST SP800-53 r4: AC-2, AC-3, IA-2, IA-8, SI-4
• PCI-DSS v3.2.1: 8.2, 8.3

Azure Policy Examples:
• No applicable built-in policy (requires configuration-based implementation)
• Use Azure AD Identity Secure Score recommendations
• Implement Azure AD security baseline configurations
• Monitor privileged account activities through Azure AD logs",ai_analysis,,Validated
,,,,,,,,,,,,,
=== ANALYSIS METADATA ===,,,,,,,,,,,,,
Total Findings: 14,,,,,,,,,,,,,
Validation Success Rate: 100.0%,,,,,,,,,,,,,
Corrections Made: 0,,,,,,,,,,,,,
Fictional IDs Prevented: 0,,,,,,,,,,,,,
Cross-Reference Findings: 0,,,,,,,,,,,,,
Analysis Timestamp: 2025-06-24T23:02:11.225404,,,,,,,,,,,,,
,,,,,,,,,,,,,
=== REFERENCE LINKS GUIDE ===,,,,,,,,,,,,,
The 'Reference Links' column contains URLs for implementation guidance,,,,,,,,,,,,,
The 'Azure Guidance' column contains Microsoft's specific recommendations,,,,,,,,,,,,,
The 'Implementation Context' column contains additional documentation links,,,,,,,,,,,,,
Links are formatted as: [Description](URL) for easy access,,,,,,,,,,,,,
