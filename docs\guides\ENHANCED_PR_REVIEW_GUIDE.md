# Enhanced IaC Guardian PR Review Guide

## Overview

The enhanced IaC Guardian PR review feature provides comprehensive security analysis capabilities with automated branch creation, pull request generation, and structured security recommendations. This guide covers the new functionality and usage patterns.

## New Features

### 🌿 Auto-Branch Creation
- Automatically creates feature branches with timestamp naming
- Pattern: `iac-guardian-security-fixes-{timestamp}`
- Based on main/master branch with latest commits

### 🔍 Comprehensive Repository Analysis
- Repository-wide security scanning
- Supports ARM templates, Bicep files, Terraform configurations
- Intelligent file filtering and size limits
- Rate limiting and timeout protection

### 📋 Automated PR Creation
- Creates pull requests with detailed security findings
- Comprehensive PR descriptions with severity breakdowns
- Domain priority analysis (Identity → Network → Data Protection)
- Professional formatting for leadership review

### 💬 Enhanced PR Comments
- Individual comments for each security finding
- Specific file path and line number references
- Azure Security Benchmark control IDs
- Threat actor perspective explanations
- Code examples in remediation guidance
- Deployment worthiness scoring

### 📊 Integrated Reporting
- HTML report generation with Glass UI
- Interactive visualizations and charts
- Export capabilities (JSON, CSV)
- Report references in PR comments

## Usage

### Command Line Interface

#### Analyze Existing PR (Original Functionality)
```bash
python security_pr_review.py --repo-id "12345678-90ab-cdef-1234-567890abcdef" --pr-id 123
```

#### Create New PR with Security Recommendations (New Functionality)
```bash
python security_pr_review.py --repo-id "12345678-90ab-cdef-1234-567890abcdef" --create-pr
```

### Environment Variables

Ensure these environment variables are configured in your `.env` file:

```bash
# Azure DevOps Configuration
AZURE_DEVOPS_PAT=your_personal_access_token
AZURE_DEVOPS_ORG=your_organization_name
AZURE_DEVOPS_PROJECT=your_project_name

# Azure OpenAI Configuration
AZURE_OPENAI_ENDPOINT=your_openai_endpoint
AZURE_OPENAI_API_KEY=your_openai_api_key
AZURE_OPENAI_DEPLOYMENT=your_deployment_name

# Analysis Configuration (Optional)
ENFORCE_DOMAIN_PRIORITY=true
USE_OPTIMIZED_PROMPTS=true
ANALYSIS_SEED=42
```

## Workflow Examples

### Scenario 1: Security Team Review
```bash
# Create comprehensive security analysis PR
python security_pr_review.py --repo-id "abc123" --create-pr

# Result: New PR with detailed security findings and recommendations
```

### Scenario 2: Developer Integration
```bash
# Analyze specific PR during code review
python security_pr_review.py --repo-id "abc123" --pr-id 456

# Result: Security comments added to existing PR
```

### Scenario 3: CI/CD Integration
```yaml
# Azure DevOps Pipeline Example
- task: PythonScript@0
  displayName: 'IaC Security Analysis'
  inputs:
    scriptSource: 'filePath'
    scriptPath: 'security_pr_review.py'
    arguments: '--repo-id $(Build.Repository.ID) --create-pr'
```

## Enhanced Comment Structure

### Individual Finding Comments
Each security finding generates a structured comment with:

```markdown
# 🔴 Security Finding: NS-1

## 📋 Finding Details
- **Severity**: 🔴 **CRITICAL**
- **Control ID**: `NS-1` (Azure Security Benchmark v3.0)
- **File**: `templates/network.json`
- **Line**: 45
- **Deployment Worthiness Score**: 95/100

## 🔍 Security Issue
Network Security Group allows unrestricted inbound access from the internet.

## 🎯 Threat Actor Perspective
**Network Attack Vector**: An attacker could exploit this network configuration to:
- Gain unauthorized access to resources
- Perform lateral movement within the infrastructure
- Intercept or manipulate network traffic
- Establish persistent access points

## 🛠️ Recommended Fix
```json
{
  "securityRules": [{
    "properties": {
      "sourceAddressPrefix": "10.0.0.0/8",
      "access": "Allow"
    }
  }]
}
```
```

### Summary Comment
Comprehensive overview including:
- Total findings by severity
- Domain priority breakdown
- Overall security posture assessment
- Next steps and recommendations
- Links to detailed HTML reports

## Error Handling

The enhanced functionality includes robust error handling for:

### Repository Access Issues
- **Permission Errors**: Clear messages for insufficient PAT permissions
- **Repository Not Found**: Validation of repository ID and access
- **Network Issues**: Retry logic with exponential backoff

### Rate Limiting
- **Azure DevOps API**: Automatic retry with delays
- **File Processing**: Batched requests with pauses
- **Large Repositories**: File count and size limits

### Timeout Protection
- **Repository Analysis**: Configurable timeouts for large repos
- **File Processing**: Individual file timeout handling
- **API Calls**: Request timeout management

## Configuration Options

### File Processing Limits
```python
max_files = 100          # Maximum files to analyze
max_file_size = 1MB      # Maximum individual file size
batch_size = 10          # Files processed before rate limit pause
```

### Retry Configuration
```python
max_retries = 3          # Maximum retry attempts
backoff_factor = 2       # Exponential backoff multiplier
rate_limit_delay = 1-6s  # Delay range for rate limiting
```

## Testing

### Test Script Usage
```bash
# Test functionality without creating actual PR
python test_enhanced_pr_review.py --repo-id "your-repo-id" --test-mode

# Create actual PR for testing
python test_enhanced_pr_review.py --repo-id "your-repo-id" --create-actual-pr
```

### Validation Checklist
- [ ] Environment variables configured
- [ ] Azure DevOps PAT has required permissions
- [ ] Repository access confirmed
- [ ] Azure OpenAI service available
- [ ] File processing limits appropriate

## Integration with Existing Features

### Glass UI Reports
- Maintains compatibility with existing HTML report generation
- Enhanced visualizations for PR-created findings
- Interactive charts and filtering capabilities

### MCP Server Integration
- Compatible with VSCode Copilot integration
- Can be triggered through MCP server commands
- Maintains existing tool functionality

### Security Analysis Engine
- Uses same threat actor-centric methodology
- Consistent with existing analysis quality
- Maintains deployment worthiness scoring

## Troubleshooting

### Common Issues

#### "Insufficient permissions" Error
- Verify Azure DevOps PAT has Code (read/write) permissions
- Ensure PAT has Pull Request (read/write) permissions
- Check repository access in Azure DevOps

#### "Repository analysis timed out" Error
- Repository may be too large for comprehensive analysis
- Consider analyzing specific folders instead
- Increase timeout limits in configuration

#### "Rate limit exceeded" Error
- Automatic retry logic should handle this
- If persistent, increase delay between requests
- Consider analyzing smaller batches of files

### Debug Mode
```bash
# Enable detailed logging
export DEBUG=true
python security_pr_review.py --repo-id "abc123" --create-pr
```

## Best Practices

### Repository Preparation
1. Ensure repository has clear IaC file organization
2. Remove unnecessary large files before analysis
3. Use .gitignore to exclude build artifacts

### PR Management
1. Review generated PRs promptly
2. Use PR comments for team collaboration
3. Close PRs after implementing fixes

### Security Workflow
1. Run analysis before major deployments
2. Address critical/high findings immediately
3. Plan medium/low findings for future sprints
4. Use reports for security posture tracking

## Support

For issues and questions:
1. Check the troubleshooting section above
2. Review Azure DevOps PAT permissions
3. Validate environment variable configuration
4. Run test script to isolate issues
5. Check logs for detailed error information
