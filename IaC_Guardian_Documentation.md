# IaC Guardian GPT - Comprehensive Documentation

## Table of Contents

1. [Overview](#overview)
2. [Architecture](#architecture)
3. [Features](#features)
4. [Installation & Setup](#installation--setup)
5. [Usage](#usage)
6. [Security Analysis Engine](#security-analysis-engine)
7. [MCP Server Integration](#mcp-server-integration)
8. [Report Generation](#report-generation)
9. [Configuration](#configuration)
10. [API Reference](#api-reference)
11. [Development](#development)
12. [Troubleshooting](#troubleshooting)
13. [Contributing](#contributing)

## Overview

IaC Guardian GPT is a comprehensive Infrastructure as Code (IaC) security analysis tool powered by Azure OpenAI and Azure Security Benchmark v3.0. It provides intelligent security analysis for Azure ARM templates, Bicep files, and Terraform configurations with advanced threat actor-centric evaluation.

### Key Capabilities

- **Multi-format Support**: Analyzes ARM templates, Bicep files, and Terraform configurations
- **Azure Security Benchmark v3.0 Compliance**: Based on Microsoft's official security framework
- **Threat Actor Perspective**: Simulates adversarial analysis to identify real-world attack vectors
- **Intelligent Report Generation**: Creates responsive HTML reports with Glass UI design
- **MCP Server Integration**: Integrates with VSCode Copilot and other AI development tools
- **Template Parameter Expansion**: Advanced parameter resolution and cross-reference analysis
- **Domain Priority Ordering**: Prioritizes security controls by domain (Identity → Network → Data Protection)

## Architecture

### Core Components

```
IaCGuardianGPT/
├── src/                          # Source code
│   ├── core/                     # Core application modules
│   │   ├── security_opt.py       # Main security analysis engine
│   │   ├── security_pr_review.py # PR review functionality
│   │   ├── enhanced_resource_control_mappings.py # Resource mapping
│   │   └── template_parameter_expander.py # Template expansion
│   ├── analysis/                 # Security analysis modules
│   │   ├── azure_security_benchmark_patterns.py
│   │   └── get_security_controls.py
│   ├── reporting/                # Report generation
│   │   └── generate_html_report.py
│   └── mcp/                      # MCP server implementation
│       └── mcp_server.py
├── tests/                        # Test files
├── docs/                         # Documentation
├── config/                       # Configuration files
├── data/                         # Data files and benchmarks
├── reports/                      # Generated reports
└── master_controls_db/          # Master controls database system
```

### Security Analysis Workflow

1. **Resource Identification**: Automatically detect Azure resource types from IaC templates
2. **Control Selection**: Map relevant Azure Security Benchmark controls based on resource types
3. **Context Evaluation**: Analyze variable usage patterns and semantic meaning
4. **Threat Analysis**: Apply adversarial thinking to identify attack vectors
5. **Impact Assessment**: Calculate blast radius and defense-in-depth gaps
6. **Validation**: Verify line numbers and filter false positives
7. **Prioritization**: Assign P0-P4 threat levels based on exploitation potential

## Features

### Security Analysis Features

- **Adversarial Analysis**: Simulates how malicious actors would approach the infrastructure
- **Context-Aware Analysis**: Examines variable usage patterns and actual values
- **Consistent Recommendations**: Deterministic analysis with caching for reproducible results
- **False Positive Reduction**: 85%+ accuracy through semantic analysis and context awareness
- **Deployment Worthiness Scoring**: Only reports threats with score ≥80

### Supported File Types

- Terraform (`.tf`)
- Bicep (`.bicep`)
- ARM Templates (`.json`, `.arm`)
- YAML (`.yml`, `.yaml`)
- Scripts (`.ps1`, `.sh`)

### Security Controls Coverage

The tool implements 27 Azure Security Benchmark v3.0 controls across 5 domains:

- **Identity Management (IM)**: IM-1 through IM-5
- **Network Security (NS)**: NS-1 through NS-5
- **Data Protection (DP)**: DP-1 through DP-5
- **Privileged Access (PA)**: PA-1 through PA-8
- **Logging and Threat Detection (LT)**: LT-1 through LT-4

## Installation & Setup

### Prerequisites

- Python 3.8 or higher
- Azure DevOps organization with repositories
- Azure OpenAI service instance
- Required Python packages (see `requirements.txt`)

### Basic Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd IaCGuardianGPT
```

2. Install dependencies:
```bash
pip install -r config/requirements.txt
```

3. Configure environment variables:
```bash
cp .env.example .env
# Edit .env with your Azure OpenAI credentials
```

### MCP Server Setup

For VSCode Copilot integration:

```bash
python setup_mcp.py
```

This will automatically:
- Install all required dependencies
- Configure VS Code settings for MCP integration
- Create launch and test scripts
- Set up proper environment variables

## Usage

### Command Line Analysis

#### Single File Analysis
```bash
python src/core/security_opt.py --file path/to/template.json
```

#### Folder Analysis
```bash
python src/core/security_opt.py --folder path/to/templates/
```

#### PR Review
```bash
python src/core/security_pr_review.py --repo-id "12345678-90ab-cdef-1234-567890abcdef" --pr-id 123
```

### MCP Server Usage in VSCode

Once configured, use these commands in GitHub Copilot Chat:

#### Analyze a Single File
```
@iac-guardian analyze_iac_file file_path="./templates/storage.json" format="markdown"
```

#### Analyze a Folder
```
@iac-guardian analyze_iac_folder folder_path="./templates" format="summary" export_report=true
```

#### Get Security Controls
```
@iac-guardian get_security_controls resource_type="Storage" domain="Data Protection"
```

#### Validate Configuration
```
@iac-guardian validate_security_config resource_type="Microsoft.Storage/storageAccounts" resource_config='{"properties":{"supportsHttpsTrafficOnly":false}}'
```

## Security Analysis Engine

### Threat Actor Perspective Methodology

The system implements adversarial analysis by simulating how malicious actors would approach the infrastructure:

- **Attack Vector Identification**: Identifies potential entry points and exploitation paths
- **Blast Radius Assessment**: Calculates potential impact and lateral movement opportunities
- **Defense-in-Depth Evaluation**: Assesses layered security controls and gaps
- **Forensic Preparedness**: Evaluates logging and monitoring capabilities for incident response

### Scoring System

- **P0 (Critical)**: Immediate exploitation risk, high blast radius
- **P1 (High)**: Significant security gaps, moderate blast radius
- **P2 (Medium)**: Security improvements needed, limited blast radius
- **P3 (Low)**: Best practice recommendations, minimal risk
- **P4 (Info)**: Informational findings, no immediate risk

### Context Evaluation

The system performs sophisticated context analysis:

- **Variable Pattern Analysis**: Examines how variables are used across templates
- **Semantic Meaning Extraction**: Understands the intent behind configurations
- **Cross-Reference Validation**: Validates relationships between resources
- **Parameter Expansion**: Resolves template parameters for accurate analysis

## MCP Server Integration

### Available Tools

1. **`analyze_iac_file`** - Analyze a single IaC file for security issues
2. **`analyze_iac_folder`** - Analyze all IaC files in a folder
3. **`get_security_controls`** - Get Azure Security Benchmark controls for resource types
4. **`validate_security_config`** - Validate specific resource configurations

### Configuration

MCP server configuration in VS Code settings:

```json
{
  "github.copilot.chat.experimental.mcp.enabled": true,
  "github.copilot.chat.experimental.mcp.servers": {
    "iac-guardian": {
      "command": "python",
      "args": ["mcp_server.py"],
      "cwd": "c:\\Users\\<USER>\\REPOS\\IaCGuardianGPT",
      "env": {
        "ENFORCE_DOMAIN_PRIORITY": "true",
        "USE_OPTIMIZED_PROMPTS": "true",
        "ANALYSIS_SEED": "42"
      }
    }
  }
}
```

## Report Generation

### HTML Reports

The tool generates responsive HTML reports with Glass UI design featuring:

- **Interactive Visualizations**: Charts and graphs for security metrics
- **Responsive Design**: Optimized for various screen sizes
- **Glass UI Framework**: Modern visual design with transparency effects
- **Code Snippets**: Clickable code blocks with line numbers
- **Severity-based Styling**: Color-coded findings based on severity levels

### Report Formats

- **HTML**: Interactive reports with Glass UI design
- **Markdown**: Technical documentation format
- **CSV**: Structured data for analysis and integration
- **JSON**: Machine-readable format for API integration

### Report Locations

Reports are saved to:
- `reports/security_findings/` - Main security analysis reports
- `reports/demo/` - Demo reports
- `reports/archived/` - Archived reports

## Configuration

### Environment Variables

Key configuration options in `.env`:

```bash
# Azure OpenAI Configuration
AZURE_OPENAI_ENDPOINT=your-endpoint
AZURE_OPENAI_API_KEY=your-api-key
AZURE_OPENAI_DEPLOYMENT_NAME=your-deployment

# Analysis Configuration
ENFORCE_DOMAIN_PRIORITY=true
USE_OPTIMIZED_PROMPTS=true
ANALYSIS_SEED=42
DEPLOYMENT_THRESHOLD=80
FOUNDATIONAL_SECURITY_BONUS=30

# Report Configuration
GENERATE_HTML_REPORTS=true
GENERATE_MARKDOWN_REPORTS=true
GENERATE_CSV_REPORTS=true
```

### Security Controls Configuration

The system supports configurable security control priorities:

- **Identity Management (IM)**: High priority scoring
- **Network Security (NS)**: High priority scoring  
- **Data Protection (DP)**: Configurable priority scoring
- **Privileged Access (PA)**: Standard priority scoring
- **Logging and Threat Detection (LT)**: Standard priority scoring

## API Reference

### Core Functions

#### `analyze_iac_file(file_path, format="json")`
Analyzes a single IaC file for security issues.

**Parameters:**
- `file_path` (str): Path to the IaC file
- `format` (str): Output format ("json", "markdown", "summary")

**Returns:**
- Analysis results in specified format

#### `analyze_iac_folder(folder_path, export_report=False)`
Analyzes all IaC files in a folder.

**Parameters:**
- `folder_path` (str): Path to the folder containing IaC files
- `export_report` (bool): Whether to generate HTML report

**Returns:**
- Comprehensive analysis results for all files

#### `get_security_controls(resource_type, domain=None)`
Retrieves relevant Azure Security Benchmark controls.

**Parameters:**
- `resource_type` (str): Azure resource type
- `domain` (str, optional): Security domain filter

**Returns:**
- List of applicable security controls

## Development

### Project Structure

The project follows a modular architecture with clear separation of concerns:

- **Core Modules**: Main application logic and security analysis engine
- **Analysis Modules**: Security pattern detection and control mapping
- **Reporting Modules**: Report generation and formatting
- **MCP Integration**: Model Context Protocol server implementation
- **Testing**: Comprehensive unit and integration tests

### Testing

Run the test suite:

```bash
# Unit tests
python -m pytest tests/unit/

# Integration tests
python -m pytest tests/integration/

# All tests
python -m pytest tests/
```

### Contributing

1. Fork the repository
2. Create a feature branch
3. Implement changes with tests
4. Run the test suite
5. Submit a pull request

## Troubleshooting

### Common Issues

#### MCP Server Not Responding
- Verify Python dependencies are installed
- Check VS Code MCP configuration
- Review server logs in VS Code Developer Tools

#### Analysis Results Inconsistent
- Ensure `ANALYSIS_SEED` is set for reproducible results
- Verify Azure OpenAI service availability
- Check template parameter resolution

#### Reports Not Generating
- Verify output directory permissions
- Check disk space availability
- Review error logs for specific issues

### Debug Mode

Enable debug logging:

```bash
export DEBUG=true
python src/core/security_opt.py --file template.json --debug
```

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- Azure Security Benchmark v3.0
- Azure OpenAI Service
- Azure DevOps REST APIs
- Model Context Protocol (MCP) specification

## Support

For issues and questions:
1. Check the troubleshooting section
2. Review existing GitHub issues
3. Create a new issue with detailed information
4. Include logs and configuration details
