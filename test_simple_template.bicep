// Simple test Bicep template for Markdown export testing
param location string = resourceGroup().location
param storageAccountName string

resource storageAccount 'Microsoft.Storage/storageAccounts@2021-04-01' = {
  name: storageAccountName
  location: location
  kind: 'StorageV2'
  sku: {
    name: 'Standard_LRS'
  }
  properties: {
    allowBlobPublicAccess: true  // This should trigger a security finding
    minimumTlsVersion: 'TLS1_0'  // This should also trigger a security finding
    supportsHttpsTrafficOnly: false  // Another security issue
  }
}
