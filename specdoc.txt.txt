[Team Logo/Project Graphic]	[Title] 


Spec Status: (Guidance)
Document Goal: The intent of this document is to guide teams on authoring the combined Functional and Dev Design spec. This is designed for teams who want to provide more detailed requirements and design considerations.  

1	Overview (PM)
1.1	Elevator Pitch / Narrative 
Guidance: Create a story that provides a high level overview – detail out the customer, their problem or goal, and then specific outcomes the customer will achieve or how success would be measured. Avoid implementation details that may restrict solution choices.

1.2	Customers
Guidance: Specify the target users or system.
<Text>
1.3	Problem Statement and Supporting Customer Insights 
Guidance: State the problem or challenge in a way that ties back to the target user. What is their goal? Why does this matter to them? Make the problem real & relevant through customer data or insights. See examples listed in the guidance page. 
<Text>

1.4	Existing Solutions or Expectations
Guidance: List the various ways in which a user may currently tackle this problem/challenge. With what expectations will customers approach our solution (competitors or current behaviors)? 
<Text>
1.5	Goals/Non-Goals
Guidance: Provide a bulleted list of goals and non-goals.
<Text>
------------------ END OF ONE – PAGER----------------------------------------------------------------------
2	<USER> <GROUP> Success (PM)
2.1	Expected Impact: Business, Customer, and Technology Outcomes, Experiments + Measures
Guidance: Identify the business, customer, and technology outcomes you expect to achieve as a result of delivering on this scenario – and then define the measures you will use to gauge progress. Ongoing iteration on the outcomes, and how to measure them, will be critical to success.  Consider leveraging experiments to enable data-driven decisions. Learn more about running Windows-related experiments here. 
No.	Outcome	Measure	Target	Pri
				
				

3	Requirements
3.1	Terminology (PM/Dev)
Term		Definition
Start entering text here.
	Start entering text here.

3.2	User Interface Storyboard (PM)
Guidance: This section is optional for UI/UX related spec. Alternatively, the user can also create Storyboard in PowerPoint and provide link to the PPT in this section.
<Storyboard>

3.3	Functional Requirements (PM)
Guidance: What feature functionality is required to deliver the outcomes listed above?
No.	Requirement	Pri
		
		

3.4	Measure Requirements (PM)
Guidance: What are the metrics, data, or telemetry that are required to perform the measurements listed above?
No.	Requirement	Pri
		
		


4	Dependencies
4.1	Feature Dependencies (PM/Dev)

4.1.1	Dependencies this design has on other Features
Team Name	Contacts	Mitigation/Fallback
	PM	Dev	Quality	
				
				

4.1.2	Features that have a dependency on this design

Team Name	Contacts
	PM	Dev	Quality
			
			


5	Design Decisions
5.1	Technology Decisions (Dev)
Guidance: Discuss alternatives, including Open Source Solutions.
<Text>
5.2	Architecture Decisions (Dev)
Guidance: Things to consider for this section:  OneCore implications, Dev Platform Convergence, OS Convergence, building on others and towards One Microsoft, use of online Services, significant performance (such as creation of new service)., compatibility, security impact (such as opening new port).
<Text>
5.3	Packaging Decisions (Dev)
Guidance: Describe packaging - Binaries and Binary Dependencies, Layering and Other Build Concerns, Product Differentiation and SKU, Product-Specific/OneCore Concerns, Processor-Specific Concerns, Build & KIT Concerns, Update OS and Manufacturing OS, etc
<Text>


6	Detailed Design (Dev)
Guidance: This section should include architectural diagrams and descriptions, implementation details, testing considerations, and applicable items from the review checklist.
<Text>
6.1	Architectural Overview

6.1.1	Diagram
<Text>

6.1.2	Description
<Text>

6.2	Interfaces and Interactions

6.2.1	Public API Added/Changed 
<Text>

6.2.2	Internal API Added/Changed
<Text>

6.2.3	Format and Protocol Added/Changed
<Text>

6.2.4	Persisted Data Format
Guidance: If you have persisted data, discuss: data format, where it is stored, versioning plan, roaming strategy, Security/PII, migration strategy, backup strategy. If not new, describe the upgrade behavior from previous.
<Text>

6.2.5	Breaking Changes
Guidance: Call out any breaking changes in any of the above, and what is done to guarantee upgrade experience (apps still work, setting preserved, etc.).
<Text>

6.2.6	Tools Impact


6.3	Telemetry, Supportability and Flighting
6.3.1	Telemetry
Guidance: Leverage Data team and think broader in terms of what we can do with telemetry (e.g. more end to end scenario delight).
<Text>

6.3.2	Logging
<Text>

6.3.3	Debugging Extensions
<Text>

6.4	Setup and Data Migration
6.4.1	Installation
<Text>

6.4.2	Migration and Upgrade
<Text>

6.4.3	OEM Customization

6.4.4	Servicing 

6.4.4.1	Updatability
<Text>

6.4.4.2	Restorability
<Text>

6.5	Functional and Unit Testing

6.5.1	Test Approach
Guidance: Summarize the overall approach to the functional testing of this feature. The summary should be concise, including a summary and rationale for what will and will not be automated.  Also should call out any gaps in test coverage, and mitigations (e.g. functionality that will be validated in scenario testing). Consider leveraging open source software and test cases in your test approach. 
<Text>

6.5.2	Test Cases
Guidance: This section contains a list of the functional test cases for the feature. These are the set of tests that must be completed and passing 100% before the feature is declared code complete.
<Text>

6.5.3	Automated Test Cases
Guidance: Describe new automated tests. And/or test ported over from existing collateral.
<Text>

6.5.4	Manual Test Cases
Guidance: This section should be empty. Only in the most exception cases.
<Text>

6.6	Gating Criteria
Guidance: Describe the subset of tests above which will be added to integration gates for RI.
<Text>

6.6.1	Test Design

6.6.1.1	Technology Decisions
Guidance: Describe key technology decisions related to testing this feature.  Include key alternatives considered and rationale for decision.  
<Text>

6.6.1.2	Architectural Overview
Guidance: Describe a high level overview of the test architecture for this feature. Include a discussion of Test Hooks or designed for testability where appropriate. 
<Text>

6.6.1.3	Detailed Design
Guidance: Describe further details around the artifacts used for manual and automated testing.
<Text>

7	Review Checklist (PM/Dev)
Guidance: Use this section as a checklist to validate your functional and design doc. Remove the items/areas that are not applicable to your spec. 
Area	X 	Consideration
Requirements		Are the requirements clear?
Are the requirements complete?
Are the requirements verifiable?
Are the requirements associated with valid scenarios?
Completeness		Have all terms been defined?
Does the design meet all requirements?
Have all key design decisions been addressed and documented?
Accessibility (Tenet)		Features and scenarios just work for users with impairments and that leverage assistive technologies. Visit the tenet site to get started.
Global Readiness (Tenet)		A combination of GeoPolitical, World Readiness, and Crypto Disclosure, activity centers on ensuring that content poses minimal or no legal or business risk in any market as well as consideration for cultural information, keyboards, reading direction, payment options and address layouts. Visit the GeoPol tenet site and World Readiness site for more information.
Protocols (Tenet)		Networking protocols are documented according to Microsoft and regulatory requirements. Visit the tenet site to get started.
Privacy and Online Safety (Tenet)		Protecting customer data from misuse, and ensuring services and features are safe and secure. Visit the tenet site to get started.
Security (Tenet)		Making Windows experiences the most secure and privacy-protecting OS on the market. Visit the tenet site to get started. Includes FIPS.
Ensure your feature works with Windows Information Protection (formerly EDP). WIP dev guidance here.
Manageability (Tenet)		Windows features must provide a complete and consistent management experience to System Administrators and IT Professionals. This is particularly important for the Enterprise and Education segments. Visit the tenet site to get started.
Application compatibility
	As we move to more continuous upgrade model, this will be increasingly important. We also need to support upgrade from Windows 7, so compatibility goes back to then.
Performance and power usage
	We cannot regress in areas (changes that have impact on global system wide performance/battery life in particular).
Health		Are data, storage, and battery requirements discussed?
Functional testing		Test Design and Approach, including what will and will not be automated, and any gaps in test coverage? 
Any RI gating criteria?
Key technology decisions related to testing this feature described, include key alternatives considered?
Test architecture overview provided, including a discussion of Test Hooks or designed for testability where appropriate?
Usage of TAP Validation? 
Interfaces and Interactions		Public API Added/Changed?
Internal API Added/Changed?
Format and Protocol Added/Changed?
Regulatory Compliance Requirements for N & KN SKUs – Private API Usage (Media Specific)		To comply with regulatory requirements for Windows, any Windows binary included in the Media Feature Package customers install to restore the functionalities excluded on regulatory Windows Desktop editions are required to NOT call undocumented APIs from other OS components or other Microsoft High Volume Products. Please visit this OneNote page for additional details. 
Answer the following questions:
1.	Is your feature adding a new Windows binary that implements audio/video playback functionality? ☐
If the answer is yes, proceed to question #3 below.  
2.	Is your feature implemented in any of the binaries on the list of Media Feature Package: ☐  
If answer is no, you are done, proceed to the next section. Otherwise, proceed to question #3 below.
3.	If your answer to question #1 OR question #2 is yes, does your feature design require call to any undocumented API in Windows? ☐
Your response to question #3 must be no to comply with regulatory requirement. You are required to email nskusup alias to review your feature.
Persisted Data Format		If you have persisted data, do you discuss: data format, where it is stored, versioning plan, roaming strategy, Security/PII, migration strategy, backup strategy?
Breaking Changes		Any breaking changes, and what is done to guarantee upgrade experience (apps still work, setting preserved, etc.)?
Tools Impact		Any impact to tools?
Deployment and Update		Do you consider how your feature gets installed and configured by OEMs, as well as any implications it might have for servicing?
•	For example, if your feature introduces a breaking change, you might need to write a migration plugin to seamlessly handle the transition when updating an existing device to a newer build.
Updatability?
Restorability?
Telemetry, Supportability and Flighting		Telemetry (leverage Data team and think broader in terms of what we can do with telemetry (e.g. more end to end scenario delight)?
Logging?
Debugging Extensions?
Feedback Collection – SUIF, etc.?
Componentization		Packaging Decisions?
Binaries and Binary Dependencies?
Layering and Other Build Concerns?
Product Differentiation and SKU?
Product-Specific/OneCore Concerns?
Processor-Specific Concerns?
Build & KIT Concerns?
Update OS and Manufacturing OS?
Source Code Layout and Depots?

8	Open Issues (PM/Dev)
<Text>

9	Prioritized Deliverable List and Estimates (Dev)
Guidance: Key deliverables to transfer to Azure DevOps, leave “blank” if already populated. If partner teams need to directly contribute to this work, then explicitly include their deliverables here so their existence and place in the priority stack is known and clear to everyone involved.
Pri	Name	Owner	Estimate	Description
1	First Deliverable	Person1	3 weeks	Implement the first deliverable
2	Second Deliverable	Person2	2 weeks	Implement the second deliverable. Depends on First Deliverable.
3	Third Deliverable	Person2	1 weeks	Implement the third deliverable.

10	Cut Deliverables and Behavior (PM/Dev)
<Text>

APPENDIX
11	References (PM/Dev)
<Link to other docs>

12	Feature Q&A/Decisions (PM/Dev)
Guidance: Capture issues raised in spec/architectural review.
<Text>
