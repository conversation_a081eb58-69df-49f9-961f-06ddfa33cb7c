#!/usr/bin/env python3
"""
Test script for enhanced IaC Guardian PR review functionality.

This script tests the new features:
1. Auto-branch creation
2. Repository-wide security analysis
3. Automated PR creation
4. Enhanced structured PR comments
5. HTML report generation

Usage:
    python test_enhanced_pr_review.py --repo-id "your-repo-id" --test-mode
"""

import os
import sys
import argparse
import logging
from pathlib import Path
from dotenv import load_dotenv

# Add src/core to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src" / "core"))

from security_pr_review import SecurityPRReviewer

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_enhanced_pr_creation(repo_id: str, test_mode: bool = True):
    """Test the enhanced PR creation functionality.
    
    Args:
        repo_id: Repository ID to test with
        test_mode: If True, performs validation without creating actual PR
    """
    try:
        print("🧪 Testing Enhanced IaC Guardian PR Review Functionality")
        print("=" * 60)
        
        # Load environment variables
        load_dotenv()
        
        # Validate required environment variables
        required_vars = [
            "AZURE_DEVOPS_PAT",
            "AZURE_DEVOPS_ORG", 
            "AZURE_DEVOPS_PROJECT",
            "AZURE_OPENAI_ENDPOINT",
            "AZURE_OPENAI_API_KEY"
        ]
        
        missing_vars = []
        for var in required_vars:
            if not os.environ.get(var):
                missing_vars.append(var)
        
        if missing_vars:
            print(f"❌ Missing required environment variables: {', '.join(missing_vars)}")
            print("Please ensure your .env file contains all required variables.")
            return False
        
        print("✅ Environment variables validated")
        
        if test_mode:
            print("🔍 Running in TEST MODE - no actual PR will be created")
            
            # Test initialization
            print("\n1. Testing SecurityPRReviewer initialization...")
            try:
                reviewer = SecurityPRReviewer(repo_id=repo_id, create_pr=True)
                print("✅ SecurityPRReviewer initialized successfully")
            except Exception as e:
                print(f"❌ Initialization failed: {str(e)}")
                return False
            
            # Test Azure DevOps connection
            print("\n2. Testing Azure DevOps connection...")
            try:
                if reviewer.ado_connection:
                    git_client = reviewer._get_git_client()
                    project = os.environ.get("AZURE_DEVOPS_PROJECT")
                    repo = git_client.get_repository(repository_id=repo_id, project=project)
                    print(f"✅ Connected to repository: {repo.name}")
                else:
                    print("❌ Azure DevOps connection not established")
                    return False
            except Exception as e:
                print(f"❌ Azure DevOps connection failed: {str(e)}")
                return False
            
            # Test benchmark preparation
            print("\n3. Testing benchmark preparation...")
            try:
                reviewer.prepare_benchmark()
                print("✅ Azure Security Benchmark prepared successfully")
            except Exception as e:
                print(f"❌ Benchmark preparation failed: {str(e)}")
                return False
            
            # Test repository file retrieval (limited)
            print("\n4. Testing repository file retrieval...")
            try:
                files = reviewer.get_repository_files()
                print(f"✅ Found {len(files)} IaC files in repository")
                
                if files:
                    print("📁 Sample files found:")
                    for i, file_info in enumerate(files[:5]):  # Show first 5 files
                        print(f"   • {file_info['path']}")
                    if len(files) > 5:
                        print(f"   ... and {len(files) - 5} more files")
                else:
                    print("ℹ️ No IaC files found in repository")
                    
            except Exception as e:
                print(f"❌ Repository file retrieval failed: {str(e)}")
                return False
            
            # Test analysis on sample files (if available)
            if files:
                print("\n5. Testing security analysis...")
                try:
                    # Analyze only first few files to avoid long test times
                    sample_files = files[:3]
                    findings = reviewer.analyze_files(sample_files)
                    print(f"✅ Security analysis completed - found {len(findings)} findings")
                    
                    if findings:
                        print("🔍 Sample findings:")
                        for i, finding in enumerate(findings[:3]):
                            severity = finding.get("severity", "UNKNOWN")
                            control_id = finding.get("control_id", "N/A")
                            file_path = finding.get("file_path", "Unknown")
                            print(f"   • {severity} - {control_id} in {file_path}")
                        if len(findings) > 3:
                            print(f"   ... and {len(findings) - 3} more findings")
                            
                except Exception as e:
                    print(f"❌ Security analysis failed: {str(e)}")
                    return False
            
            print("\n✅ All tests passed! Enhanced PR review functionality is working correctly.")
            print("\n💡 To create an actual PR, run:")
            print(f"   python security_pr_review.py --repo-id \"{repo_id}\" --create-pr")
            
        else:
            print("🚀 Creating actual security recommendations PR...")
            
            # Create actual PR
            reviewer = SecurityPRReviewer(repo_id=repo_id, create_pr=True)
            reviewer.run()
            
            if hasattr(reviewer, 'created_pr_id'):
                print(f"\n✅ Successfully created security recommendations PR!")
                print(f"📋 PR ID: {reviewer.created_pr_id}")
                print(f"🌿 Branch: {reviewer.created_branch_name}")
                print(f"🔗 Repository: {repo_id}")
            else:
                print("❌ PR creation completed but PR ID not available")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
        logger.exception("Test execution failed")
        return False

def main():
    """Main test execution function."""
    parser = argparse.ArgumentParser(
        description="Test enhanced IaC Guardian PR review functionality"
    )
    parser.add_argument("--repo-id", required=True, help="Repository ID to test with")
    parser.add_argument("--test-mode", action="store_true", default=True,
                       help="Run in test mode (no actual PR creation)")
    parser.add_argument("--create-actual-pr", action="store_true",
                       help="Create actual PR (overrides test mode)")
    
    args = parser.parse_args()
    
    # Determine test mode
    test_mode = args.test_mode and not args.create_actual_pr
    
    if not test_mode:
        confirm = input("⚠️ This will create an actual PR. Continue? (y/N): ")
        if confirm.lower() != 'y':
            print("Operation cancelled.")
            return
    
    success = test_enhanced_pr_creation(args.repo_id, test_mode)
    
    if success:
        print("\n🎉 Test completed successfully!")
        sys.exit(0)
    else:
        print("\n💥 Test failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()
