#!/usr/bin/env python3
"""
Azure DevOps Setup Validation Script

This script helps validate your Azure DevOps configuration and Personal Access Token (PAT)
before running the enhanced IaC Guardian PR review functionality.
"""

import os
import sys
from pathlib import Path
from dotenv import load_dotenv
import requests
import base64
from datetime import datetime

def validate_environment_variables():
    """Validate required environment variables."""
    print("🔍 Validating Environment Variables...")
    
    required_vars = {
        "AZURE_DEVOPS_PAT": "Personal Access Token",
        "AZURE_DEVOPS_ORG": "Organization name", 
        "AZURE_DEVOPS_PROJECT": "Project name"
    }
    
    missing_vars = []
    for var, description in required_vars.items():
        value = os.environ.get(var)
        if not value:
            missing_vars.append(f"  • {var} ({description})")
            print(f"❌ {var}: Not set")
        else:
            # Mask PAT for security
            if "PAT" in var:
                masked_value = value[:8] + "..." + value[-4:] if len(value) > 12 else "***"
                print(f"✅ {var}: {masked_value}")
            else:
                print(f"✅ {var}: {value}")
    
    if missing_vars:
        print(f"\n❌ Missing required environment variables:")
        for var in missing_vars:
            print(var)
        print(f"\nPlease add these to your .env file.")
        return False
    
    print("✅ All required environment variables are set")
    return True

def validate_pat_permissions():
    """Validate PAT permissions using Azure DevOps REST API."""
    print("\n🔑 Validating Personal Access Token...")
    
    pat = os.environ.get("AZURE_DEVOPS_PAT")
    org = os.environ.get("AZURE_DEVOPS_ORG")
    
    if not pat or not org:
        print("❌ PAT or organization not configured")
        return False
    
    # Ensure org is in URL format
    if not org.startswith("https://"):
        org_url = f"https://dev.azure.com/{org}"
    else:
        org_url = org
    
    # Test basic authentication
    try:
        # Encode PAT for basic auth
        credentials = f":{pat}"
        encoded_credentials = base64.b64encode(credentials.encode("utf-8")).decode("utf-8")
        headers = {
            "Authorization": f"Basic {encoded_credentials}",
            "Content-Type": "application/json"
        }
        
        # Test connection with a simple API call
        test_url = f"{org_url}/_apis/projects"
        print(f"Testing connection to: {test_url}")
        
        response = requests.get(test_url, headers=headers, params={"api-version": "6.0"}, timeout=30)
        
        if response.status_code == 200:
            projects = response.json().get("value", [])
            print(f"✅ PAT is valid and working")
            print(f"✅ Found {len(projects)} accessible projects")
            
            # List accessible projects
            if projects:
                print("📁 Accessible projects:")
                for project in projects[:5]:  # Show first 5 projects
                    print(f"   • {project.get('name', 'Unknown')}")
                if len(projects) > 5:
                    print(f"   ... and {len(projects) - 5} more projects")
            
            return True
            
        elif response.status_code == 401:
            print("❌ PAT authentication failed")
            print("   • PAT may be expired or invalid")
            print("   • Check if PAT is correctly set in .env file")
            return False
            
        elif response.status_code == 403:
            print("❌ PAT has insufficient permissions")
            print("   • PAT needs 'Project and team (read)' permission")
            return False
            
        else:
            print(f"❌ Unexpected response: {response.status_code}")
            print(f"   Response: {response.text[:200]}")
            return False
            
    except requests.exceptions.Timeout:
        print("❌ Request timed out")
        print("   • Check your internet connection")
        print("   • Verify organization URL is correct")
        return False
        
    except requests.exceptions.RequestException as e:
        print(f"❌ Request failed: {str(e)}")
        return False

def validate_project_access():
    """Validate access to specific project."""
    print("\n📁 Validating Project Access...")
    
    pat = os.environ.get("AZURE_DEVOPS_PAT")
    org = os.environ.get("AZURE_DEVOPS_ORG")
    project = os.environ.get("AZURE_DEVOPS_PROJECT")
    
    if not all([pat, org, project]):
        print("❌ Missing required configuration")
        return False
    
    # Ensure org is in URL format
    if not org.startswith("https://"):
        org_url = f"https://dev.azure.com/{org}"
    else:
        org_url = org
    
    try:
        credentials = f":{pat}"
        encoded_credentials = base64.b64encode(credentials.encode("utf-8")).decode("utf-8")
        headers = {
            "Authorization": f"Basic {encoded_credentials}",
            "Content-Type": "application/json"
        }
        
        # Test project access
        project_url = f"{org_url}/{project}/_apis/git/repositories"
        print(f"Testing project access: {project}")
        
        response = requests.get(project_url, headers=headers, params={"api-version": "6.0"}, timeout=30)
        
        if response.status_code == 200:
            repos = response.json().get("value", [])
            print(f"✅ Project access confirmed")
            print(f"✅ Found {len(repos)} repositories in project")
            
            if repos:
                print("📚 Available repositories:")
                for repo in repos[:3]:  # Show first 3 repos
                    print(f"   • {repo.get('name', 'Unknown')} (ID: {repo.get('id', 'Unknown')[:8]}...)")
                if len(repos) > 3:
                    print(f"   ... and {len(repos) - 3} more repositories")
            
            return True
            
        elif response.status_code == 404:
            print(f"❌ Project '{project}' not found")
            print("   • Check project name spelling")
            print("   • Verify you have access to this project")
            return False
            
        elif response.status_code == 403:
            print(f"❌ Access denied to project '{project}'")
            print("   • PAT needs 'Code (read)' permission")
            print("   • Verify you're a member of this project")
            return False
            
        else:
            print(f"❌ Unexpected response: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Project validation failed: {str(e)}")
        return False

def print_pat_creation_guide():
    """Print guide for creating a new PAT."""
    print("\n🔑 How to Create/Update Your Personal Access Token:")
    print("="*60)
    print("1. Go to: https://dev.azure.com/your-org/_usersSettings/tokens")
    print("2. Click 'New Token'")
    print("3. Set these permissions:")
    print("   • Code: Read & write")
    print("   • Pull Request: Read & write") 
    print("   • Project and team: Read")
    print("4. Set expiration (recommend 90 days or less)")
    print("5. Copy the token immediately (you won't see it again)")
    print("6. Update your .env file:")
    print("   AZURE_DEVOPS_PAT=your_new_token_here")
    print("7. Restart your application")

def main():
    """Main validation function."""
    print("🛡️ IaC Guardian - Azure DevOps Setup Validation")
    print("="*60)
    print(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Load environment variables
    load_dotenv()
    
    # Run validations
    env_valid = validate_environment_variables()
    
    if not env_valid:
        print_pat_creation_guide()
        return False
    
    pat_valid = validate_pat_permissions()
    
    if not pat_valid:
        print_pat_creation_guide()
        return False
    
    project_valid = validate_project_access()
    
    if not project_valid:
        print_pat_creation_guide()
        return False
    
    print("\n🎉 All validations passed!")
    print("✅ Your Azure DevOps setup is ready for IaC Guardian")
    print("\nYou can now run:")
    print("  python test_enhanced_pr_review.py --repo-id 'your-repo-id' --test-mode")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⚠️ Validation cancelled by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Validation failed with error: {str(e)}")
        sys.exit(1)
