#!/usr/bin/env python3
"""
Test script for Markdown export functionality in IaC Guardian.

This script tests the new Markdown report generation feature with team comment placeholders.
"""

import os
import sys
from pathlib import Path

# Add the src/core directory to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "src" / "core"))

def test_markdown_export():
    """Test the Markdown export functionality with sample data."""
    try:
        from security_opt import SecurityPRReviewer
        
        print("🧪 Testing Markdown Export Functionality")
        print("=" * 50)
        
        # Set up environment for testing
        os.environ.setdefault('ENFORCE_DOMAIN_PRIORITY', 'true')
        os.environ.setdefault('USE_OPTIMIZED_PROMPTS', 'true')
        os.environ.setdefault('ANALYSIS_SEED', '42')
        
        # Initialize the reviewer
        reviewer = SecurityPRReviewer(local_folder=".")
        
        # Create sample findings for testing
        sample_findings = [
            {
                "file_path": "templates/storage-account.bicep",
                "line": 15,
                "control_id": "DP-1",
                "severity": "HIGH",
                "description": "Storage account allows public blob access which could expose sensitive data",
                "remediation": "Set allowBlobPublicAccess to false to prevent unauthorized data access",
                "source": "ai_analysis"
            },
            {
                "file_path": "templates/network-security-group.json",
                "line": 23,
                "control_id": "NS-2",
                "severity": "CRITICAL",
                "description": "Network Security Group allows unrestricted inbound access from internet (0.0.0.0/0)",
                "remediation": "Restrict source IP ranges to specific trusted networks or IP addresses",
                "source": "ai_analysis"
            },
            {
                "file_path": "templates/key-vault.bicep",
                "line": 8,
                "control_id": "IM-3",
                "severity": "MEDIUM",
                "description": "Key Vault does not have network access restrictions configured",
                "remediation": "Configure network ACLs to restrict access to Key Vault from specific networks",
                "source": "ai_analysis"
            }
        ]
        
        print(f"📊 Created {len(sample_findings)} sample security findings")
        
        # Create test output directory
        test_output_dir = project_root / "test_reports"
        test_output_dir.mkdir(exist_ok=True)
        
        print(f"📁 Test output directory: {test_output_dir}")
        
        # Test Markdown export
        print("\n🔍 Testing Markdown export...")
        reviewer.export_findings(sample_findings, format="markdown", output_dir=str(test_output_dir))
        
        # Check if Markdown file was created
        markdown_files = list(test_output_dir.glob("*.md"))
        if markdown_files:
            latest_md = max(markdown_files, key=lambda x: x.stat().st_mtime)
            print(f"✅ Markdown report generated: {latest_md.name}")
            print(f"📄 File size: {latest_md.stat().st_size:,} bytes")
            
            # Display a preview of the content
            with open(latest_md, 'r', encoding='utf-8') as f:
                content = f.read()
                lines = content.split('\n')
                preview_lines = lines[:30]  # Show first 30 lines
                
            print(f"\n📋 Preview of Markdown report (first 30 lines):")
            print("-" * 60)
            for line in preview_lines:
                print(line)
            print("-" * 60)
            print(f"... (showing {len(preview_lines)} of {len(lines)} total lines)")
            
            # Check for team comment placeholders
            if "Team Review Section" in content:
                print("✅ Team comment placeholders found in report")
            else:
                print("❌ Team comment placeholders missing from report")
                
            if "Security Team Review:" in content:
                print("✅ Security team section found")
            if "Development Team Review:" in content:
                print("✅ Development team section found")
            if "DevOps/Infrastructure Team Review:" in content:
                print("✅ DevOps team section found")
            if "Action Items:" in content:
                print("✅ Action items section found")
            if "Resolution Status:" in content:
                print("✅ Resolution status section found")
                
        else:
            print("❌ No Markdown files found in output directory")
            return False
        
        # Test 'all' format export
        print("\n🔍 Testing 'all' format export (HTML + CSV + Markdown)...")
        reviewer.export_findings(sample_findings, format="all", output_dir=str(test_output_dir))
        
        # Check all file types were created
        html_files = list(test_output_dir.glob("*.html"))
        csv_files = list(test_output_dir.glob("*.csv"))
        markdown_files = list(test_output_dir.glob("*.md"))
        
        print(f"📊 Generated files:")
        print(f"   - HTML reports: {len(html_files)}")
        print(f"   - CSV reports: {len(csv_files)}")
        print(f"   - Markdown reports: {len(markdown_files)}")
        
        if html_files and csv_files and markdown_files:
            print("✅ All report formats generated successfully!")
        else:
            print("❌ Some report formats missing")
            return False
            
        print(f"\n🎉 Markdown export test completed successfully!")
        print(f"📁 All test reports saved to: {test_output_dir}")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("Please ensure you are running from the IaC Guardian root directory")
        return False
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_markdown_export()
    sys.exit(0 if success else 1)
