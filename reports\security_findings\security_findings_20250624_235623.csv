File Path,Control ID,Control Domain,Control Name,Severity,Line,Description,Remediation,Reference Links,Azure Guidance,Implementation Context,Source,Cross Reference Type,Validation Status
app-config.bicep,IM-1,Identity Management,Use centralized identity and authentication system,HIGH,1.0,Privilege escalation risk: Security-sensitive parameter 'keyValues' flows across template boundary,"Secure parameter 'keyValues' and validate its usage across template boundaries

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/active-directory/develop/single-and-multi-tenant-apps) | [Create and configure Azure AD](https://docs.microsoft.com/azure/active-directory/fundamentals/active-directory-access-create-new-tenant) | [Define Azure AD tenants](https://azure.microsoft.com/resources/securing-azure-environments-with-azure-active-directory/) | [External identity providers](https://docs.microsoft.com/azure/active-directory/b2b/identity-providers) | [Identity and key management guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys)

🔵 Azure Guidance: Standardize on Azure AD for identity and authentication management across Microsoft cloud resources, organizational resources, and enterprise identities. Migrate on-premises Active Directory applicati...",[Enhanced Implementation Context](https://docs.microsoft.com/azure/active-directory/develop/single-and-multi-tenant-apps) | [Create and configure Azure AD](https://docs.microsoft.com/azure/active-directory/fundamentals/active-directory-access-create-new-tenant) | [Define Azure AD tenants](https://azure.microsoft.com/resources/securing-azure-environments-with-azure-active-directory/) | [External identity providers](https://docs.microsoft.com/azure/active-directory/b2b/identity-providers) | [Identity and key management guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys),"Standardize on Azure AD for identity and authentication management across Microsoft cloud resources, organizational resources, and enterprise identities. Migrate on-premises Active Directory applications to Azure AD when technically feasible.","Enhanced Implementation Context:
• Azure AD tenancy concepts: https://docs.microsoft.com/azure/active-directory/develop/single-and-multi-tenant-apps
• Create and configure Azure AD: https://docs.microsoft.com/azure/active-directory/fundamentals/active-directory-access-create-new-tenant
• Define Azure AD tenants: https://azure.microsoft.com/resources/securing-azure-environments-with-azure-active-directory/
• External identity providers: https://docs.microsoft.com/azure/active-directory/b2b/identity-providers
• Identity and key management guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys

Compliance Mappings:
• CIS Controls v8: 6.7, 12.5
• NIST SP800-53 r4: AC-2, AC-3, IA-2, IA-8
• PCI-DSS v3.2.1: 7.2, 8.3

Azure Policy Examples:
• An Azure Active Directory administrator should be provisioned for SQL servers
• Service Fabric clusters should only use Azure Active Directory for client authentication
• Standardize identity provider across all applications and services",cross_reference_analysis,privilege_escalation,Validated
app-config.bicep,NS-2,Network Security,Secure cloud services with network controls,CRITICAL,13.0,"The App Configuration resource (Microsoft.AppConfiguration/configurationStores) at line 13 does not specify network access restrictions or private endpoint configuration. By default, App Configuration endpoints are publicly accessible, which enables an attacker to perform initial access, data exfiltration, or lateral movement if credentials are compromised. The blast radius includes potential unauthorized access to all configuration data and secrets stored in the App Configuration instance.","Restrict public network access by setting 'publicNetworkAccess' to 'Disabled' and configure a private endpoint for the App Configuration resource. This ensures only trusted networks can access the configuration store. Reference: Azure Security Benchmark NS-2. Example: Add 'publicNetworkAccess: ""Disabled""' under properties and define a 'privateEndpointConnections' block.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/private-link/private-link-overview) | [Private endpoints for Storage](https://docs.microsoft.com/azure/storage/common/storage-private-endpoints) | [SQL private endpoints](https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview) | [Key Vault private endpoints](https://docs.microsoft.com/azure/key-vault/general/private-link-service) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture)

🔵 Azure Guidance: Deploy private endpoints for Azure resources supporting Private Link. Disable public network access where feasible. Use VNet integration for services requiring private access.",[Enhanced Implementation Context](https://docs.microsoft.com/azure/private-link/private-link-overview) | [Private endpoints for Storage](https://docs.microsoft.com/azure/storage/common/storage-private-endpoints) | [SQL private endpoints](https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview) | [Key Vault private endpoints](https://docs.microsoft.com/azure/key-vault/general/private-link-service) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture),Deploy private endpoints for Azure resources supporting Private Link. Disable public network access where feasible. Use VNet integration for services requiring private access.,"Enhanced Implementation Context:
• Private Link overview: https://docs.microsoft.com/azure/private-link/private-link-overview
• Private endpoints for Storage: https://docs.microsoft.com/azure/storage/common/storage-private-endpoints
• SQL private endpoints: https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview
• Key Vault private endpoints: https://docs.microsoft.com/azure/key-vault/general/private-link-service
• Security architecture guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture

Compliance Mappings:
• CIS Controls v8: 3.12, 4.4
• NIST SP800-53 r4: AC-4, SC-2, SC-7
• PCI-DSS v3.2.1: 1.1, 1.2, 1.3

Azure Policy Examples:
• Private endpoint should be enabled for PostgreSQL/MySQL/MariaDB servers
• Storage accounts should use private link
• Azure SQL Database should disable public network access
• Cognitive Services accounts should restrict network access
• Container registries should use private link",ai_analysis,,Validated
function-settings.bicep,IM-1,Identity Management,Use centralized identity and authentication system,HIGH,1.0,Privilege escalation risk: Security-sensitive parameter 'app_insights_key' flows across template boundary,"Secure parameter 'app_insights_key' and validate its usage across template boundaries

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/active-directory/develop/single-and-multi-tenant-apps) | [Create and configure Azure AD](https://docs.microsoft.com/azure/active-directory/fundamentals/active-directory-access-create-new-tenant) | [Define Azure AD tenants](https://azure.microsoft.com/resources/securing-azure-environments-with-azure-active-directory/) | [External identity providers](https://docs.microsoft.com/azure/active-directory/b2b/identity-providers) | [Identity and key management guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys)

🔵 Azure Guidance: Standardize on Azure AD for identity and authentication management across Microsoft cloud resources, organizational resources, and enterprise identities. Migrate on-premises Active Directory applicati...",[Enhanced Implementation Context](https://docs.microsoft.com/azure/active-directory/develop/single-and-multi-tenant-apps) | [Create and configure Azure AD](https://docs.microsoft.com/azure/active-directory/fundamentals/active-directory-access-create-new-tenant) | [Define Azure AD tenants](https://azure.microsoft.com/resources/securing-azure-environments-with-azure-active-directory/) | [External identity providers](https://docs.microsoft.com/azure/active-directory/b2b/identity-providers) | [Identity and key management guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys),"Standardize on Azure AD for identity and authentication management across Microsoft cloud resources, organizational resources, and enterprise identities. Migrate on-premises Active Directory applications to Azure AD when technically feasible.","Enhanced Implementation Context:
• Azure AD tenancy concepts: https://docs.microsoft.com/azure/active-directory/develop/single-and-multi-tenant-apps
• Create and configure Azure AD: https://docs.microsoft.com/azure/active-directory/fundamentals/active-directory-access-create-new-tenant
• Define Azure AD tenants: https://azure.microsoft.com/resources/securing-azure-environments-with-azure-active-directory/
• External identity providers: https://docs.microsoft.com/azure/active-directory/b2b/identity-providers
• Identity and key management guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys

Compliance Mappings:
• CIS Controls v8: 6.7, 12.5
• NIST SP800-53 r4: AC-2, AC-3, IA-2, IA-8
• PCI-DSS v3.2.1: 7.2, 8.3

Azure Policy Examples:
• An Azure Active Directory administrator should be provisioned for SQL servers
• Service Fabric clusters should only use Azure Active Directory for client authentication
• Standardize identity provider across all applications and services",cross_reference_analysis,privilege_escalation,Validated
function-settings.bicep,IM-8,Identity Management,Restrict the exposure of credential and secrets,HIGH,42.0,"The parameter 'ado_access_client_id' on line 42 is assigned a hardcoded client ID value ('c77cd33b-8c5b-4d5c-a7a7-bdea70b062b1'). Hardcoding sensitive identifiers or secrets in infrastructure code can enable attackers with access to the codebase or deployment logs to impersonate or target the associated application registration. This increases the risk of credential exposure, lateral movement, and privilege escalation if the client ID is used in conjunction with other leaked secrets or misconfigurations. The blast radius includes potential unauthorized access to Azure resources or external systems integrated with this client ID.","Remove the hardcoded client ID from the parameter definition on line 42. Instead, require the value to be provided securely at deployment time (e.g., via Azure Key Vault reference or secure pipeline variable). Ensure all sensitive identifiers and secrets are managed outside of source code and implement credential scanning in CI/CD pipelines. Reference: ASB IM-8.

📚 References: [Enhanced Implementation Context](https://secdevtools.azurewebsites.net/helpcredscan.html) | [GitHub secret scanning](https://docs.github.com/github/administering-a-repository/about-secret-scanning) | [Azure Key Vault developer guide](https://docs.microsoft.com/azure/key-vault/general/developers-guide) | [Secure coding practices](https://docs.microsoft.com/azure/security/develop/secure-dev-overview) | [Application security guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-application-security-devsecops)

🔵 Azure Guidance: Store secrets in Azure Key Vault instead of embedding in code. Implement Azure DevOps Credential Scanner or GitHub secret scanning. Use managed identities for Azure Key Vault access.",[Enhanced Implementation Context](https://secdevtools.azurewebsites.net/helpcredscan.html) | [GitHub secret scanning](https://docs.github.com/github/administering-a-repository/about-secret-scanning) | [Azure Key Vault developer guide](https://docs.microsoft.com/azure/key-vault/general/developers-guide) | [Secure coding practices](https://docs.microsoft.com/azure/security/develop/secure-dev-overview) | [Application security guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-application-security-devsecops),Store secrets in Azure Key Vault instead of embedding in code. Implement Azure DevOps Credential Scanner or GitHub secret scanning. Use managed identities for Azure Key Vault access.,"Enhanced Implementation Context:
• Azure DevOps Credential Scanner setup: https://secdevtools.azurewebsites.net/helpcredscan.html
• GitHub secret scanning: https://docs.github.com/github/administering-a-repository/about-secret-scanning
• Azure Key Vault developer guide: https://docs.microsoft.com/azure/key-vault/general/developers-guide
• Secure coding practices: https://docs.microsoft.com/azure/security/develop/secure-dev-overview
• Application security guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-application-security-devsecops

Compliance Mappings:
• CIS Controls v8: 16.9, 16.12
• NIST SP800-53 r4: IA-5
• PCI-DSS v3.2.1: 3.5, 6.3, 8.2

Azure Policy Examples:
• No applicable built-in policy (requires development process implementation)
• Implement credential scanning in CI/CD pipelines
• Enforce Azure Key Vault usage for secret storage
• Monitor and audit secret access patterns
• Require managed identities for Azure service authentication",ai_analysis,,Validated
function-settings.bicep,NS-2,Network Security,Secure cloud services with network controls,CRITICAL,46.0,"The 'Microsoft.Storage/storageAccounts' resource at line 46 is referenced as 'existing' with no evidence of private endpoint usage or public network access restriction. If public network access is enabled, attackers can directly target the storage account over the internet, leading to potential data exfiltration or service compromise.","Ensure the referenced storage account has 'publicNetworkAccess' set to 'Disabled' and a private endpoint configured. Update the storage account resource or its configuration to enforce private access only, and verify that all consumers use the private endpoint.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/private-link/private-link-overview) | [Private endpoints for Storage](https://docs.microsoft.com/azure/storage/common/storage-private-endpoints) | [SQL private endpoints](https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview) | [Key Vault private endpoints](https://docs.microsoft.com/azure/key-vault/general/private-link-service) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture)

🔵 Azure Guidance: Deploy private endpoints for Azure resources supporting Private Link. Disable public network access where feasible. Use VNet integration for services requiring private access.",[Enhanced Implementation Context](https://docs.microsoft.com/azure/private-link/private-link-overview) | [Private endpoints for Storage](https://docs.microsoft.com/azure/storage/common/storage-private-endpoints) | [SQL private endpoints](https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview) | [Key Vault private endpoints](https://docs.microsoft.com/azure/key-vault/general/private-link-service) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture),Deploy private endpoints for Azure resources supporting Private Link. Disable public network access where feasible. Use VNet integration for services requiring private access.,"Enhanced Implementation Context:
• Private Link overview: https://docs.microsoft.com/azure/private-link/private-link-overview
• Private endpoints for Storage: https://docs.microsoft.com/azure/storage/common/storage-private-endpoints
• SQL private endpoints: https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview
• Key Vault private endpoints: https://docs.microsoft.com/azure/key-vault/general/private-link-service
• Security architecture guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture

Compliance Mappings:
• CIS Controls v8: 3.12, 4.4
• NIST SP800-53 r4: AC-4, SC-2, SC-7
• PCI-DSS v3.2.1: 1.1, 1.2, 1.3

Azure Policy Examples:
• Private endpoint should be enabled for PostgreSQL/MySQL/MariaDB servers
• Storage accounts should use private link
• Azure SQL Database should disable public network access
• Cognitive Services accounts should restrict network access
• Container registries should use private link",ai_analysis,,Validated
function-settings.bicep,NS-2,Network Security,Secure cloud services with network controls,CRITICAL,50.0,"The 'Microsoft.Web/sites' (App Service) resource at line 50 is referenced as 'existing' with no evidence of VNet integration or private endpoint configuration. Without VNet integration, the App Service may be accessible from the public internet, increasing the risk of initial access and lateral movement by attackers.","Integrate the App Service with an Azure Virtual Network (VNet) using VNet integration or private endpoints. Update the App Service configuration to restrict public access and ensure only trusted networks can reach the application.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/private-link/private-link-overview) | [Private endpoints for Storage](https://docs.microsoft.com/azure/storage/common/storage-private-endpoints) | [SQL private endpoints](https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview) | [Key Vault private endpoints](https://docs.microsoft.com/azure/key-vault/general/private-link-service) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture)

🔵 Azure Guidance: Deploy private endpoints for Azure resources supporting Private Link. Disable public network access where feasible. Use VNet integration for services requiring private access.",[Enhanced Implementation Context](https://docs.microsoft.com/azure/private-link/private-link-overview) | [Private endpoints for Storage](https://docs.microsoft.com/azure/storage/common/storage-private-endpoints) | [SQL private endpoints](https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview) | [Key Vault private endpoints](https://docs.microsoft.com/azure/key-vault/general/private-link-service) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture),Deploy private endpoints for Azure resources supporting Private Link. Disable public network access where feasible. Use VNet integration for services requiring private access.,"Enhanced Implementation Context:
• Private Link overview: https://docs.microsoft.com/azure/private-link/private-link-overview
• Private endpoints for Storage: https://docs.microsoft.com/azure/storage/common/storage-private-endpoints
• SQL private endpoints: https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview
• Key Vault private endpoints: https://docs.microsoft.com/azure/key-vault/general/private-link-service
• Security architecture guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture

Compliance Mappings:
• CIS Controls v8: 3.12, 4.4
• NIST SP800-53 r4: AC-4, SC-2, SC-7
• PCI-DSS v3.2.1: 1.1, 1.2, 1.3

Azure Policy Examples:
• Private endpoint should be enabled for PostgreSQL/MySQL/MariaDB servers
• Storage accounts should use private link
• Azure SQL Database should disable public network access
• Cognitive Services accounts should restrict network access
• Container registries should use private link",ai_analysis,,Validated
function-settings.bicep,IM-3,Identity Management,Manage application identities securely and automatically,HIGH,50.0,"The 'Microsoft.Web/sites' (App Service) resource at line 50 is declared as 'existing' without any evidence of a managed identity configuration. Absence of a managed identity for App Service enables attackers to exploit credential exposure or require the use of less secure authentication methods, increasing the risk of privilege escalation and lateral movement if the app needs to access other Azure resources.","Update the App Service resource definition to include a system-assigned or user-assigned managed identity. For example, add 'identity: { type: 'SystemAssigned' }' to the resource configuration. Ensure all downstream resources (e.g., Storage, Key Vault) grant access to this managed identity instead of using credentials.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/active-directory/managed-identities-azure-resources/overview) | [Services supporting managed identities](https://docs.microsoft.com/azure/active-directory/managed-identities-azure-resources/services-support-managed-identities) | [Azure service principal creation](https://docs.microsoft.com/powershell/azure/create-azure-service-principal-azureps) | [Service principal with certificates](https://docs.microsoft.com/azure/active-directory/develop/howto-authenticate-service-principal-powershell) | [Identity and key management guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys)

🔵 Azure Guidance: Use Azure managed identities for services supporting Azure AD authentication. For services without managed identity support use Azure AD service principals with certificate credentials and restricted ...",[Enhanced Implementation Context](https://docs.microsoft.com/azure/active-directory/managed-identities-azure-resources/overview) | [Services supporting managed identities](https://docs.microsoft.com/azure/active-directory/managed-identities-azure-resources/services-support-managed-identities) | [Azure service principal creation](https://docs.microsoft.com/powershell/azure/create-azure-service-principal-azureps) | [Service principal with certificates](https://docs.microsoft.com/azure/active-directory/develop/howto-authenticate-service-principal-powershell) | [Identity and key management guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys),Use Azure managed identities for services supporting Azure AD authentication. For services without managed identity support use Azure AD service principals with certificate credentials and restricted permissions.,"Enhanced Implementation Context:
• Azure managed identities overview: https://docs.microsoft.com/azure/active-directory/managed-identities-azure-resources/overview
• Services supporting managed identities: https://docs.microsoft.com/azure/active-directory/managed-identities-azure-resources/services-support-managed-identities
• Azure service principal creation: https://docs.microsoft.com/powershell/azure/create-azure-service-principal-azureps
• Service principal with certificates: https://docs.microsoft.com/azure/active-directory/develop/howto-authenticate-service-principal-powershell
• Identity and key management guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys

Compliance Mappings:
• CIS Controls v8: Not specified
• NIST SP800-53 r4: AC-2, AC-3, IA-4, IA-5, IA-9
• PCI-DSS v3.2.1: Not applicable

Azure Policy Examples:
• Managed identity should be used in your Function App
• Managed identity should be used in your Web App
• Service principals should be used to protect your subscriptions instead of management certificates
• Managed identity should be used in your API App
• Virtual machines' Guest Configuration extension should be deployed with system-assigned managed identity",ai_analysis,,Validated
function-settings.bicep,IM-8,Identity Management,Restrict the exposure of credential and secrets,HIGH,66.0,"The configuration property 'APPINSIGHTS_INSTRUMENTATIONKEY' is set directly as an application setting. If this value is a secret (such as an instrumentation key or connection string), storing it in plain text in app settings exposes it to anyone with access to the App Service configuration. Attackers who gain access to the configuration can exfiltrate this key and send unauthorized telemetry or pivot to other Azure resources, increasing the blast radius of a compromise.","Store all sensitive values, such as 'APPINSIGHTS_INSTRUMENTATIONKEY', in Azure Key Vault and reference them using Key Vault references in your app settings. Ensure the App Service has a managed identity with access to the Key Vault. Remove any plain text secrets from the configuration. Reference: ASB IM-8.

📚 References: [Enhanced Implementation Context](https://secdevtools.azurewebsites.net/helpcredscan.html) | [GitHub secret scanning](https://docs.github.com/github/administering-a-repository/about-secret-scanning) | [Azure Key Vault developer guide](https://docs.microsoft.com/azure/key-vault/general/developers-guide) | [Secure coding practices](https://docs.microsoft.com/azure/security/develop/secure-dev-overview) | [Application security guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-application-security-devsecops)

🔵 Azure Guidance: Store secrets in Azure Key Vault instead of embedding in code. Implement Azure DevOps Credential Scanner or GitHub secret scanning. Use managed identities for Azure Key Vault access.",[Enhanced Implementation Context](https://secdevtools.azurewebsites.net/helpcredscan.html) | [GitHub secret scanning](https://docs.github.com/github/administering-a-repository/about-secret-scanning) | [Azure Key Vault developer guide](https://docs.microsoft.com/azure/key-vault/general/developers-guide) | [Secure coding practices](https://docs.microsoft.com/azure/security/develop/secure-dev-overview) | [Application security guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-application-security-devsecops),Store secrets in Azure Key Vault instead of embedding in code. Implement Azure DevOps Credential Scanner or GitHub secret scanning. Use managed identities for Azure Key Vault access.,"Enhanced Implementation Context:
• Azure DevOps Credential Scanner setup: https://secdevtools.azurewebsites.net/helpcredscan.html
• GitHub secret scanning: https://docs.github.com/github/administering-a-repository/about-secret-scanning
• Azure Key Vault developer guide: https://docs.microsoft.com/azure/key-vault/general/developers-guide
• Secure coding practices: https://docs.microsoft.com/azure/security/develop/secure-dev-overview
• Application security guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-application-security-devsecops

Compliance Mappings:
• CIS Controls v8: 16.9, 16.12
• NIST SP800-53 r4: IA-5
• PCI-DSS v3.2.1: 3.5, 6.3, 8.2

Azure Policy Examples:
• No applicable built-in policy (requires development process implementation)
• Implement credential scanning in CI/CD pipelines
• Enforce Azure Key Vault usage for secret storage
• Monitor and audit secret access patterns
• Require managed identities for Azure service authentication",ai_analysis,,Validated
function-settings.bicep,IM-8,Identity Management,Restrict the exposure of credential and secrets,HIGH,70.0,"The configuration property 'AzureWebJobsStorage__accountName' is set directly as an application setting. If this value is used to construct a storage connection string or is otherwise sensitive, storing it in plain text increases the risk of credential exposure. Attackers with access to the configuration could use this information to enumerate or access storage accounts, leading to data exfiltration or privilege escalation.","Store all sensitive storage account connection details in Azure Key Vault and reference them securely in your app settings. Use managed identities for Azure resources to access storage accounts instead of embedding account names or keys. Reference: ASB IM-8.

📚 References: [Enhanced Implementation Context](https://secdevtools.azurewebsites.net/helpcredscan.html) | [GitHub secret scanning](https://docs.github.com/github/administering-a-repository/about-secret-scanning) | [Azure Key Vault developer guide](https://docs.microsoft.com/azure/key-vault/general/developers-guide) | [Secure coding practices](https://docs.microsoft.com/azure/security/develop/secure-dev-overview) | [Application security guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-application-security-devsecops)

🔵 Azure Guidance: Store secrets in Azure Key Vault instead of embedding in code. Implement Azure DevOps Credential Scanner or GitHub secret scanning. Use managed identities for Azure Key Vault access.",[Enhanced Implementation Context](https://secdevtools.azurewebsites.net/helpcredscan.html) | [GitHub secret scanning](https://docs.github.com/github/administering-a-repository/about-secret-scanning) | [Azure Key Vault developer guide](https://docs.microsoft.com/azure/key-vault/general/developers-guide) | [Secure coding practices](https://docs.microsoft.com/azure/security/develop/secure-dev-overview) | [Application security guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-application-security-devsecops),Store secrets in Azure Key Vault instead of embedding in code. Implement Azure DevOps Credential Scanner or GitHub secret scanning. Use managed identities for Azure Key Vault access.,"Enhanced Implementation Context:
• Azure DevOps Credential Scanner setup: https://secdevtools.azurewebsites.net/helpcredscan.html
• GitHub secret scanning: https://docs.github.com/github/administering-a-repository/about-secret-scanning
• Azure Key Vault developer guide: https://docs.microsoft.com/azure/key-vault/general/developers-guide
• Secure coding practices: https://docs.microsoft.com/azure/security/develop/secure-dev-overview
• Application security guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-application-security-devsecops

Compliance Mappings:
• CIS Controls v8: 16.9, 16.12
• NIST SP800-53 r4: IA-5
• PCI-DSS v3.2.1: 3.5, 6.3, 8.2

Azure Policy Examples:
• No applicable built-in policy (requires development process implementation)
• Implement credential scanning in CI/CD pipelines
• Enforce Azure Key Vault usage for secret storage
• Monitor and audit secret access patterns
• Require managed identities for Azure service authentication",ai_analysis,,Validated
function-settings.bicep,IM-8,Identity Management,Restrict the exposure of credential and secrets,HIGH,80.0,"The configuration property 'APPCONFIGURATION_ENDPOINT' is set directly as an application setting. If this endpoint includes credentials or is used to access sensitive configuration data, exposing it in plain text increases the risk of unauthorized access. Attackers could use this endpoint to enumerate or modify application configuration, leading to lateral movement or privilege escalation.","Store sensitive endpoints and credentials in Azure Key Vault and reference them securely in your app settings. Use managed identities for accessing Azure App Configuration and remove any plain text secrets from the configuration. Reference: ASB IM-8.

📚 References: [Enhanced Implementation Context](https://secdevtools.azurewebsites.net/helpcredscan.html) | [GitHub secret scanning](https://docs.github.com/github/administering-a-repository/about-secret-scanning) | [Azure Key Vault developer guide](https://docs.microsoft.com/azure/key-vault/general/developers-guide) | [Secure coding practices](https://docs.microsoft.com/azure/security/develop/secure-dev-overview) | [Application security guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-application-security-devsecops)

🔵 Azure Guidance: Store secrets in Azure Key Vault instead of embedding in code. Implement Azure DevOps Credential Scanner or GitHub secret scanning. Use managed identities for Azure Key Vault access.",[Enhanced Implementation Context](https://secdevtools.azurewebsites.net/helpcredscan.html) | [GitHub secret scanning](https://docs.github.com/github/administering-a-repository/about-secret-scanning) | [Azure Key Vault developer guide](https://docs.microsoft.com/azure/key-vault/general/developers-guide) | [Secure coding practices](https://docs.microsoft.com/azure/security/develop/secure-dev-overview) | [Application security guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-application-security-devsecops),Store secrets in Azure Key Vault instead of embedding in code. Implement Azure DevOps Credential Scanner or GitHub secret scanning. Use managed identities for Azure Key Vault access.,"Enhanced Implementation Context:
• Azure DevOps Credential Scanner setup: https://secdevtools.azurewebsites.net/helpcredscan.html
• GitHub secret scanning: https://docs.github.com/github/administering-a-repository/about-secret-scanning
• Azure Key Vault developer guide: https://docs.microsoft.com/azure/key-vault/general/developers-guide
• Secure coding practices: https://docs.microsoft.com/azure/security/develop/secure-dev-overview
• Application security guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-application-security-devsecops

Compliance Mappings:
• CIS Controls v8: 16.9, 16.12
• NIST SP800-53 r4: IA-5
• PCI-DSS v3.2.1: 3.5, 6.3, 8.2

Azure Policy Examples:
• No applicable built-in policy (requires development process implementation)
• Implement credential scanning in CI/CD pipelines
• Enforce Azure Key Vault usage for secret storage
• Monitor and audit secret access patterns
• Require managed identities for Azure service authentication",ai_analysis,,Validated
function-settings.bicep,IM-3,Identity Management,Manage application identities securely and automatically,HIGH,98.0,"The configuration property 'OVERRIDE_USE_MI_FIC_ASSERTION_CLIENTID' is set directly as an application setting. If this value is a client ID for a user-assigned managed identity, ensure that the managed identity is used for all resource access and that no hardcoded credentials or secrets are present. Failure to use managed identities exclusively increases the risk of credential leakage and privilege escalation.","Ensure that all resource access is performed using managed identities and that no client secrets or credentials are embedded in configuration. Remove any hardcoded credentials and use Azure Key Vault for secret management. Reference: ASB IM-3.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/active-directory/managed-identities-azure-resources/overview) | [Services supporting managed identities](https://docs.microsoft.com/azure/active-directory/managed-identities-azure-resources/services-support-managed-identities) | [Azure service principal creation](https://docs.microsoft.com/powershell/azure/create-azure-service-principal-azureps) | [Service principal with certificates](https://docs.microsoft.com/azure/active-directory/develop/howto-authenticate-service-principal-powershell) | [Identity and key management guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys)

🔵 Azure Guidance: Use Azure managed identities for services supporting Azure AD authentication. For services without managed identity support use Azure AD service principals with certificate credentials and restricted ...",[Enhanced Implementation Context](https://docs.microsoft.com/azure/active-directory/managed-identities-azure-resources/overview) | [Services supporting managed identities](https://docs.microsoft.com/azure/active-directory/managed-identities-azure-resources/services-support-managed-identities) | [Azure service principal creation](https://docs.microsoft.com/powershell/azure/create-azure-service-principal-azureps) | [Service principal with certificates](https://docs.microsoft.com/azure/active-directory/develop/howto-authenticate-service-principal-powershell) | [Identity and key management guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys),Use Azure managed identities for services supporting Azure AD authentication. For services without managed identity support use Azure AD service principals with certificate credentials and restricted permissions.,"Enhanced Implementation Context:
• Azure managed identities overview: https://docs.microsoft.com/azure/active-directory/managed-identities-azure-resources/overview
• Services supporting managed identities: https://docs.microsoft.com/azure/active-directory/managed-identities-azure-resources/services-support-managed-identities
• Azure service principal creation: https://docs.microsoft.com/powershell/azure/create-azure-service-principal-azureps
• Service principal with certificates: https://docs.microsoft.com/azure/active-directory/develop/howto-authenticate-service-principal-powershell
• Identity and key management guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys

Compliance Mappings:
• CIS Controls v8: Not specified
• NIST SP800-53 r4: AC-2, AC-3, IA-4, IA-5, IA-9
• PCI-DSS v3.2.1: Not applicable

Azure Policy Examples:
• Managed identity should be used in your Function App
• Managed identity should be used in your Web App
• Service principals should be used to protect your subscriptions instead of management certificates
• Managed identity should be used in your API App
• Virtual machines' Guest Configuration extension should be deployed with system-assigned managed identity",ai_analysis,,Validated
keyvault.bicep,NS-1,Network Security,Establish network segmentation boundaries,MEDIUM,1.0,Cross-template trust boundary: Template references external templates/modules,"Review template dependencies and ensure secure communication between templates

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/virtual-network/concepts-and-best-practices) | [Subnet management](https://docs.microsoft.com/azure/virtual-network/virtual-network-manage-subnet) | [NSG security rules](https://docs.microsoft.com/azure/virtual-network/tutorial-filter-network-traffic) | [Application security groups](https://docs.microsoft.com/azure/virtual-network/network-security-groups-overview#application-security-groups) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture)

🔵 Azure Guidance: Create virtual networks (VNets) as fundamental segmentation. Use network security groups (NSGs) and application security groups (ASGs) for traffic control. Implement deny-by-default approach.",[Enhanced Implementation Context](https://docs.microsoft.com/azure/virtual-network/concepts-and-best-practices) | [Subnet management](https://docs.microsoft.com/azure/virtual-network/virtual-network-manage-subnet) | [NSG security rules](https://docs.microsoft.com/azure/virtual-network/tutorial-filter-network-traffic) | [Application security groups](https://docs.microsoft.com/azure/virtual-network/network-security-groups-overview#application-security-groups) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture),Create virtual networks (VNets) as fundamental segmentation. Use network security groups (NSGs) and application security groups (ASGs) for traffic control. Implement deny-by-default approach.,"Enhanced Implementation Context:
• Virtual network concepts: https://docs.microsoft.com/azure/virtual-network/concepts-and-best-practices
• Subnet management: https://docs.microsoft.com/azure/virtual-network/virtual-network-manage-subnet
• NSG security rules: https://docs.microsoft.com/azure/virtual-network/tutorial-filter-network-traffic
• Application security groups: https://docs.microsoft.com/azure/virtual-network/network-security-groups-overview#application-security-groups
• Security architecture guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture

Compliance Mappings:
• CIS Controls v8: 3.12, 13.4, 4.4
• NIST SP800-53 r4: AC-4, SC-2, SC-7
• PCI-DSS v3.2.1: 1.1, 1.2, 1.3

Azure Policy Examples:
• Adaptive network hardening recommendations should be applied on internet facing virtual machines
• All network ports should be restricted on network security groups associated to your virtual machine
• Subnets should be associated with a Network Security Group",cross_reference_analysis,trust_boundary,Validated
keyvault.bicep,IM-1,Identity Management,Use centralized identity and authentication system,HIGH,1.0,Privilege escalation risk: Security-sensitive parameter 'secrets' flows across template boundary,"Secure parameter 'secrets' and validate its usage across template boundaries

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/active-directory/develop/single-and-multi-tenant-apps) | [Create and configure Azure AD](https://docs.microsoft.com/azure/active-directory/fundamentals/active-directory-access-create-new-tenant) | [Define Azure AD tenants](https://azure.microsoft.com/resources/securing-azure-environments-with-azure-active-directory/) | [External identity providers](https://docs.microsoft.com/azure/active-directory/b2b/identity-providers) | [Identity and key management guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys)

🔵 Azure Guidance: Standardize on Azure AD for identity and authentication management across Microsoft cloud resources, organizational resources, and enterprise identities. Migrate on-premises Active Directory applicati...",[Enhanced Implementation Context](https://docs.microsoft.com/azure/active-directory/develop/single-and-multi-tenant-apps) | [Create and configure Azure AD](https://docs.microsoft.com/azure/active-directory/fundamentals/active-directory-access-create-new-tenant) | [Define Azure AD tenants](https://azure.microsoft.com/resources/securing-azure-environments-with-azure-active-directory/) | [External identity providers](https://docs.microsoft.com/azure/active-directory/b2b/identity-providers) | [Identity and key management guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys),"Standardize on Azure AD for identity and authentication management across Microsoft cloud resources, organizational resources, and enterprise identities. Migrate on-premises Active Directory applications to Azure AD when technically feasible.","Enhanced Implementation Context:
• Azure AD tenancy concepts: https://docs.microsoft.com/azure/active-directory/develop/single-and-multi-tenant-apps
• Create and configure Azure AD: https://docs.microsoft.com/azure/active-directory/fundamentals/active-directory-access-create-new-tenant
• Define Azure AD tenants: https://azure.microsoft.com/resources/securing-azure-environments-with-azure-active-directory/
• External identity providers: https://docs.microsoft.com/azure/active-directory/b2b/identity-providers
• Identity and key management guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-identity-keys

Compliance Mappings:
• CIS Controls v8: 6.7, 12.5
• NIST SP800-53 r4: AC-2, AC-3, IA-2, IA-8
• PCI-DSS v3.2.1: 7.2, 8.3

Azure Policy Examples:
• An Azure Active Directory administrator should be provisioned for SQL servers
• Service Fabric clusters should only use Azure Active Directory for client authentication
• Standardize identity provider across all applications and services",cross_reference_analysis,privilege_escalation,Validated
keyvault.bicep,DP-6,Data Protection,Use a secure key management process,HIGH,1.0,Parameter flow security risk: Sensitive parameters may be exposed through template dependencies,"Use Key Vault references for sensitive parameters and validate parameter flow security

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/key-vault/general/overview) | [Azure data encryption key hierarchy](https://docs.microsoft.com/azure/security/fundamentals/encryption-atrest#key-hierarchy) | [BYOK specification](https://docs.microsoft.com/azure/key-vault/keys/byok-specification) | [Key Vault security best practices](https://docs.microsoft.com/azure/key-vault/general/best-practices)

🔵 Azure Guidance: Use Azure Key Vault to create and control encryption key lifecycle including generation distribution storage rotation and revocation. Follow best practices for key hierarchy and BYOK scenarios. Ensure...",[Enhanced Implementation Context](https://docs.microsoft.com/azure/key-vault/general/overview) | [Azure data encryption key hierarchy](https://docs.microsoft.com/azure/security/fundamentals/encryption-atrest#key-hierarchy) | [BYOK specification](https://docs.microsoft.com/azure/key-vault/keys/byok-specification) | [Key Vault security best practices](https://docs.microsoft.com/azure/key-vault/general/best-practices),Use Azure Key Vault to create and control encryption key lifecycle including generation distribution storage rotation and revocation. Follow best practices for key hierarchy and BYOK scenarios. Ensure FIPS compliance levels meet requirements.,"Enhanced Implementation Context:
• Azure Key Vault overview: https://docs.microsoft.com/azure/key-vault/general/overview
• Azure data encryption key hierarchy: https://docs.microsoft.com/azure/security/fundamentals/encryption-atrest#key-hierarchy
• BYOK specification: https://docs.microsoft.com/azure/key-vault/keys/byok-specification
• Key Vault security best practices: https://docs.microsoft.com/azure/key-vault/general/best-practices
• FIPS 140-2 compliance levels: Software-protected keys (Level 1) HSM-protected keys in vaults (Level 2) HSM-protected keys in Managed HSM (Level 3)

Compliance Mappings:
• CIS Controls v8: Not specified
• NIST SP800-53 r4: IA-5, SC-12, SC-28
• PCI-DSS v3.2.1: 3.6

Azure Policy Examples:
• Key Vault keys should have an expiration date
• Key Vault secrets should have an expiration date
• Implement key rotation policies
• Use separate data encryption keys (DEK) with key encryption keys (KEK)
• Register keys with Azure Key Vault using key IDs",cross_reference_analysis,parameter_flow,Validated
keyvault.bicep,DP-8,Data Protection,Ensure security of key and certificate repository,CRITICAL,20.0,"The Key Vault resource definition does not specify 'publicNetworkAccess', which defaults to 'Enabled'. This allows the Key Vault to be accessible from the public internet, increasing the risk of unauthorized access, brute force, and enumeration attacks. The blast radius includes potential compromise of all secrets and keys in the vault.","Explicitly set 'publicNetworkAccess' to 'Disabled' in the Key Vault resource properties to block all public internet access. Only allow access via private endpoints and trusted networks. Example: 'publicNetworkAccess: ""Disabled""'. Reference: Azure Security Benchmark v3.0 DP-8.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/key-vault/general/overview) | [Key Vault security best practices](https://docs.microsoft.com/azure/key-vault/general/best-practices) | [Managed identity Key Vault access](https://docs.microsoft.com/azure/active-directory/managed-identities-azure-resources/tutorial-windows-vm-access-nonaad) | [Key Vault Private Link](https://docs.microsoft.com/azure/key-vault/general/private-link-service) | [Key Vault logging and monitoring](https://docs.microsoft.com/azure/key-vault/general/logging)

🔵 Azure Guidance: Secure Azure Key Vault through access policies or Azure RBAC for least privilege. Use Private Link and Azure Firewall for minimal exposure. Implement separation of duties managed identity access and c...",[Enhanced Implementation Context](https://docs.microsoft.com/azure/key-vault/general/overview) | [Key Vault security best practices](https://docs.microsoft.com/azure/key-vault/general/best-practices) | [Managed identity Key Vault access](https://docs.microsoft.com/azure/active-directory/managed-identities-azure-resources/tutorial-windows-vm-access-nonaad) | [Key Vault Private Link](https://docs.microsoft.com/azure/key-vault/general/private-link-service) | [Key Vault logging and monitoring](https://docs.microsoft.com/azure/key-vault/general/logging),Secure Azure Key Vault through access policies or Azure RBAC for least privilege. Use Private Link and Azure Firewall for minimal exposure. Implement separation of duties managed identity access and comprehensive logging.,"Enhanced Implementation Context:
• Azure Key Vault overview: https://docs.microsoft.com/azure/key-vault/general/overview
• Key Vault security best practices: https://docs.microsoft.com/azure/key-vault/general/best-practices
• Managed identity Key Vault access: https://docs.microsoft.com/azure/active-directory/managed-identities-azure-resources/tutorial-windows-vm-access-nonaad
• Key Vault Private Link: https://docs.microsoft.com/azure/key-vault/general/private-link-service
• Key Vault logging and monitoring: https://docs.microsoft.com/azure/key-vault/general/logging

Compliance Mappings:
• CIS Controls v8: Not specified
• NIST SP800-53 r4: IA-5, SC-12, SC-17
• PCI-DSS v3.2.1: 3.6

Azure Policy Examples:
• Key vaults should have purge protection enabled
• Azure Defender for Key Vault should be enabled
• Key vaults should have soft delete enabled
• Azure Key Vault should disable public network access
• Private endpoint should be configured for Key Vault
• Resource logs in Key Vault should be enabled
• Implement separation of duties for key management and data access",ai_analysis,,Validated
keyvault.bicep,DP-8,Data Protection,Ensure security of key and certificate repository,CRITICAL,22.0,"The 'accessPolicies' property is set to an empty array while 'enableRbacAuthorization' is true. If RBAC is not properly configured, this can result in either over-permissive or under-protected access to the Key Vault. Attackers who gain privileged Azure roles (such as Owner or Contributor) could escalate privileges and access all secrets, keys, and certificates, increasing the blast radius to all protected data.","Review and restrict Azure RBAC assignments for the Key Vault to only those identities that require access, following the principle of least privilege. Regularly audit role assignments and ensure no broad roles (e.g., Owner, Contributor) are granted unnecessary access. Reference: Azure Security Benchmark v3.0 DP-8.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/key-vault/general/overview) | [Key Vault security best practices](https://docs.microsoft.com/azure/key-vault/general/best-practices) | [Managed identity Key Vault access](https://docs.microsoft.com/azure/active-directory/managed-identities-azure-resources/tutorial-windows-vm-access-nonaad) | [Key Vault Private Link](https://docs.microsoft.com/azure/key-vault/general/private-link-service) | [Key Vault logging and monitoring](https://docs.microsoft.com/azure/key-vault/general/logging)

🔵 Azure Guidance: Secure Azure Key Vault through access policies or Azure RBAC for least privilege. Use Private Link and Azure Firewall for minimal exposure. Implement separation of duties managed identity access and c...",[Enhanced Implementation Context](https://docs.microsoft.com/azure/key-vault/general/overview) | [Key Vault security best practices](https://docs.microsoft.com/azure/key-vault/general/best-practices) | [Managed identity Key Vault access](https://docs.microsoft.com/azure/active-directory/managed-identities-azure-resources/tutorial-windows-vm-access-nonaad) | [Key Vault Private Link](https://docs.microsoft.com/azure/key-vault/general/private-link-service) | [Key Vault logging and monitoring](https://docs.microsoft.com/azure/key-vault/general/logging),Secure Azure Key Vault through access policies or Azure RBAC for least privilege. Use Private Link and Azure Firewall for minimal exposure. Implement separation of duties managed identity access and comprehensive logging.,"Enhanced Implementation Context:
• Azure Key Vault overview: https://docs.microsoft.com/azure/key-vault/general/overview
• Key Vault security best practices: https://docs.microsoft.com/azure/key-vault/general/best-practices
• Managed identity Key Vault access: https://docs.microsoft.com/azure/active-directory/managed-identities-azure-resources/tutorial-windows-vm-access-nonaad
• Key Vault Private Link: https://docs.microsoft.com/azure/key-vault/general/private-link-service
• Key Vault logging and monitoring: https://docs.microsoft.com/azure/key-vault/general/logging

Compliance Mappings:
• CIS Controls v8: Not specified
• NIST SP800-53 r4: IA-5, SC-12, SC-17
• PCI-DSS v3.2.1: 3.6

Azure Policy Examples:
• Key vaults should have purge protection enabled
• Azure Defender for Key Vault should be enabled
• Key vaults should have soft delete enabled
• Azure Key Vault should disable public network access
• Private endpoint should be configured for Key Vault
• Resource logs in Key Vault should be enabled
• Implement separation of duties for key management and data access",ai_analysis,,Validated
keyvault.bicep,NS-2,Network Security,Secure cloud services with network controls,CRITICAL,27.0,"The 'defaultAction' property in the 'networkAcls' block for the Key Vault resource is set to 'Allow'. This configuration allows public network access to the Key Vault, enabling attackers to attempt initial access, brute force, or enumeration attacks from any IP address. The blast radius includes potential exposure of all secrets, keys, and certificates stored in the vault, as well as the risk of lateral movement if the vault is compromised.","Set 'networkAcls.defaultAction' to 'Deny' to block all public network access by default. Only allow access from explicitly defined trusted IPs or virtual networks. Additionally, enable private endpoints for the Key Vault to ensure access is only possible from within your trusted Azure VNets. Example: 'defaultAction: ""Deny""'. Reference: Azure Security Benchmark v3.0 NS-2.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/private-link/private-link-overview) | [Private endpoints for Storage](https://docs.microsoft.com/azure/storage/common/storage-private-endpoints) | [SQL private endpoints](https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview) | [Key Vault private endpoints](https://docs.microsoft.com/azure/key-vault/general/private-link-service) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture)

🔵 Azure Guidance: Deploy private endpoints for Azure resources supporting Private Link. Disable public network access where feasible. Use VNet integration for services requiring private access.",[Enhanced Implementation Context](https://docs.microsoft.com/azure/private-link/private-link-overview) | [Private endpoints for Storage](https://docs.microsoft.com/azure/storage/common/storage-private-endpoints) | [SQL private endpoints](https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview) | [Key Vault private endpoints](https://docs.microsoft.com/azure/key-vault/general/private-link-service) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture),Deploy private endpoints for Azure resources supporting Private Link. Disable public network access where feasible. Use VNet integration for services requiring private access.,"Enhanced Implementation Context:
• Private Link overview: https://docs.microsoft.com/azure/private-link/private-link-overview
• Private endpoints for Storage: https://docs.microsoft.com/azure/storage/common/storage-private-endpoints
• SQL private endpoints: https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview
• Key Vault private endpoints: https://docs.microsoft.com/azure/key-vault/general/private-link-service
• Security architecture guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture

Compliance Mappings:
• CIS Controls v8: 3.12, 4.4
• NIST SP800-53 r4: AC-4, SC-2, SC-7
• PCI-DSS v3.2.1: 1.1, 1.2, 1.3

Azure Policy Examples:
• Private endpoint should be enabled for PostgreSQL/MySQL/MariaDB servers
• Storage accounts should use private link
• Azure SQL Database should disable public network access
• Cognitive Services accounts should restrict network access
• Container registries should use private link",ai_analysis,,Validated
keyvault.bicep,DP-8,Data Protection,Ensure security of key and certificate repository,CRITICAL,27.0,"The 'bypass' property in the 'networkAcls' block is set to 'AzureServices', which allows all Azure services to bypass network restrictions and access the Key Vault. This increases the attack surface, as any Azure service (potentially in other tenants or subscriptions) could access the vault if compromised, leading to data exposure and privilege escalation.","Set 'networkAcls.bypass' to 'None' to prevent all Azure services from bypassing network rules. Only allow access from explicitly defined trusted resources and identities. Example: 'bypass: ""None""'. Reference: Azure Security Benchmark v3.0 DP-8.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/key-vault/general/overview) | [Key Vault security best practices](https://docs.microsoft.com/azure/key-vault/general/best-practices) | [Managed identity Key Vault access](https://docs.microsoft.com/azure/active-directory/managed-identities-azure-resources/tutorial-windows-vm-access-nonaad) | [Key Vault Private Link](https://docs.microsoft.com/azure/key-vault/general/private-link-service) | [Key Vault logging and monitoring](https://docs.microsoft.com/azure/key-vault/general/logging)

🔵 Azure Guidance: Secure Azure Key Vault through access policies or Azure RBAC for least privilege. Use Private Link and Azure Firewall for minimal exposure. Implement separation of duties managed identity access and c...",[Enhanced Implementation Context](https://docs.microsoft.com/azure/key-vault/general/overview) | [Key Vault security best practices](https://docs.microsoft.com/azure/key-vault/general/best-practices) | [Managed identity Key Vault access](https://docs.microsoft.com/azure/active-directory/managed-identities-azure-resources/tutorial-windows-vm-access-nonaad) | [Key Vault Private Link](https://docs.microsoft.com/azure/key-vault/general/private-link-service) | [Key Vault logging and monitoring](https://docs.microsoft.com/azure/key-vault/general/logging),Secure Azure Key Vault through access policies or Azure RBAC for least privilege. Use Private Link and Azure Firewall for minimal exposure. Implement separation of duties managed identity access and comprehensive logging.,"Enhanced Implementation Context:
• Azure Key Vault overview: https://docs.microsoft.com/azure/key-vault/general/overview
• Key Vault security best practices: https://docs.microsoft.com/azure/key-vault/general/best-practices
• Managed identity Key Vault access: https://docs.microsoft.com/azure/active-directory/managed-identities-azure-resources/tutorial-windows-vm-access-nonaad
• Key Vault Private Link: https://docs.microsoft.com/azure/key-vault/general/private-link-service
• Key Vault logging and monitoring: https://docs.microsoft.com/azure/key-vault/general/logging

Compliance Mappings:
• CIS Controls v8: Not specified
• NIST SP800-53 r4: IA-5, SC-12, SC-17
• PCI-DSS v3.2.1: 3.6

Azure Policy Examples:
• Key vaults should have purge protection enabled
• Azure Defender for Key Vault should be enabled
• Key vaults should have soft delete enabled
• Azure Key Vault should disable public network access
• Private endpoint should be configured for Key Vault
• Resource logs in Key Vault should be enabled
• Implement separation of duties for key management and data access",ai_analysis,,Validated
storage-accounts.bicep,NS-2,Network Security,Secure cloud services with network controls,CRITICAL,168.0,"The 'defaultAction' property in 'networkAcls' for 'storageAccountFunc' is set to 'Allow'. This enables public network access to the storage account, allowing any source not explicitly denied to access the resource. Attackers could exploit this to gain initial access, exfiltrate data, or move laterally within the environment. The blast radius includes potential exposure of all data in the storage account and compromise of connected services.","Set 'networkAcls.defaultAction' to 'Deny' for all storage accounts to restrict public network access. Only allow access from explicitly defined IP rules and virtual networks. Example: 'defaultAction: ""Deny""'. Reference: Azure Security Benchmark v3.0 NS-2.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/private-link/private-link-overview) | [Private endpoints for Storage](https://docs.microsoft.com/azure/storage/common/storage-private-endpoints) | [SQL private endpoints](https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview) | [Key Vault private endpoints](https://docs.microsoft.com/azure/key-vault/general/private-link-service) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture)

🔵 Azure Guidance: Deploy private endpoints for Azure resources supporting Private Link. Disable public network access where feasible. Use VNet integration for services requiring private access.",[Enhanced Implementation Context](https://docs.microsoft.com/azure/private-link/private-link-overview) | [Private endpoints for Storage](https://docs.microsoft.com/azure/storage/common/storage-private-endpoints) | [SQL private endpoints](https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview) | [Key Vault private endpoints](https://docs.microsoft.com/azure/key-vault/general/private-link-service) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture),Deploy private endpoints for Azure resources supporting Private Link. Disable public network access where feasible. Use VNet integration for services requiring private access.,"Enhanced Implementation Context:
• Private Link overview: https://docs.microsoft.com/azure/private-link/private-link-overview
• Private endpoints for Storage: https://docs.microsoft.com/azure/storage/common/storage-private-endpoints
• SQL private endpoints: https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview
• Key Vault private endpoints: https://docs.microsoft.com/azure/key-vault/general/private-link-service
• Security architecture guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture

Compliance Mappings:
• CIS Controls v8: 3.12, 4.4
• NIST SP800-53 r4: AC-4, SC-2, SC-7
• PCI-DSS v3.2.1: 1.1, 1.2, 1.3

Azure Policy Examples:
• Private endpoint should be enabled for PostgreSQL/MySQL/MariaDB servers
• Storage accounts should use private link
• Azure SQL Database should disable public network access
• Cognitive Services accounts should restrict network access
• Container registries should use private link",ai_analysis,,Validated
autoscale-settings.bicep,NS-2,Network Security,Secure cloud services with network controls,CRITICAL,2.0,"The 'targetResourceUri' property at line 16 references 'server_farm_id' but does not enforce private endpoint usage or restrict public network access. If the referenced App Service Plan or resource is accessible over the public internet, this increases the attack surface for initial access, data exfiltration, or service disruption.","Configure the referenced resource (server_farm_id) to use private endpoints and disable public network access. For App Service Plans, ensure VNet integration is enabled and public access is restricted. Apply Azure Policy to enforce private endpoint usage and monitor for any public exposure as per Azure Security Benchmark NS-2.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/private-link/private-link-overview) | [Private endpoints for Storage](https://docs.microsoft.com/azure/storage/common/storage-private-endpoints) | [SQL private endpoints](https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview) | [Key Vault private endpoints](https://docs.microsoft.com/azure/key-vault/general/private-link-service) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture)

🔵 Azure Guidance: Deploy private endpoints for Azure resources supporting Private Link. Disable public network access where feasible. Use VNet integration for services requiring private access.",[Enhanced Implementation Context](https://docs.microsoft.com/azure/private-link/private-link-overview) | [Private endpoints for Storage](https://docs.microsoft.com/azure/storage/common/storage-private-endpoints) | [SQL private endpoints](https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview) | [Key Vault private endpoints](https://docs.microsoft.com/azure/key-vault/general/private-link-service) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture),Deploy private endpoints for Azure resources supporting Private Link. Disable public network access where feasible. Use VNet integration for services requiring private access.,"Enhanced Implementation Context:
• Private Link overview: https://docs.microsoft.com/azure/private-link/private-link-overview
• Private endpoints for Storage: https://docs.microsoft.com/azure/storage/common/storage-private-endpoints
• SQL private endpoints: https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview
• Key Vault private endpoints: https://docs.microsoft.com/azure/key-vault/general/private-link-service
• Security architecture guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture

Compliance Mappings:
• CIS Controls v8: 3.12, 4.4
• NIST SP800-53 r4: AC-4, SC-2, SC-7
• PCI-DSS v3.2.1: 1.1, 1.2, 1.3

Azure Policy Examples:
• Private endpoint should be enabled for PostgreSQL/MySQL/MariaDB servers
• Storage accounts should use private link
• Azure SQL Database should disable public network access
• Cognitive Services accounts should restrict network access
• Container registries should use private link",ai_analysis,,Validated
autoscale-settings.bicep,NS-1,Network Security,Establish network segmentation boundaries,CRITICAL,10.0,"The 'Microsoft.Insights/autoscalesettings' resource at line 10 does not specify any network segmentation, network security group (NSG), or deny-by-default network controls. Without explicit network security configuration, the autoscale settings resource could be exposed to broader network access than intended, enabling lateral movement or unauthorized access if the underlying resource (e.g., App Service Plan or Storage Account) is not properly segmented. Attackers gaining access to the network could exploit this lack of segmentation to escalate privileges or disrupt services.","Ensure that all resources referenced by autoscale settings (such as server_farm_id and func_storage_account_id) are deployed within isolated virtual networks and are protected by NSGs with a deny-by-default policy. Explicitly associate subnets with NSGs and restrict inbound/outbound traffic to only what is required for autoscale operations. Review and enforce segmentation for all production workloads as per Azure Security Benchmark NS-1.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/virtual-network/concepts-and-best-practices) | [Subnet management](https://docs.microsoft.com/azure/virtual-network/virtual-network-manage-subnet) | [NSG security rules](https://docs.microsoft.com/azure/virtual-network/tutorial-filter-network-traffic) | [Application security groups](https://docs.microsoft.com/azure/virtual-network/network-security-groups-overview#application-security-groups) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture)

🔵 Azure Guidance: Create virtual networks (VNets) as fundamental segmentation. Use network security groups (NSGs) and application security groups (ASGs) for traffic control. Implement deny-by-default approach.",[Enhanced Implementation Context](https://docs.microsoft.com/azure/virtual-network/concepts-and-best-practices) | [Subnet management](https://docs.microsoft.com/azure/virtual-network/virtual-network-manage-subnet) | [NSG security rules](https://docs.microsoft.com/azure/virtual-network/tutorial-filter-network-traffic) | [Application security groups](https://docs.microsoft.com/azure/virtual-network/network-security-groups-overview#application-security-groups) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture),Create virtual networks (VNets) as fundamental segmentation. Use network security groups (NSGs) and application security groups (ASGs) for traffic control. Implement deny-by-default approach.,"Enhanced Implementation Context:
• Virtual network concepts: https://docs.microsoft.com/azure/virtual-network/concepts-and-best-practices
• Subnet management: https://docs.microsoft.com/azure/virtual-network/virtual-network-manage-subnet
• NSG security rules: https://docs.microsoft.com/azure/virtual-network/tutorial-filter-network-traffic
• Application security groups: https://docs.microsoft.com/azure/virtual-network/network-security-groups-overview#application-security-groups
• Security architecture guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture

Compliance Mappings:
• CIS Controls v8: 3.12, 13.4, 4.4
• NIST SP800-53 r4: AC-4, SC-2, SC-7
• PCI-DSS v3.2.1: 1.1, 1.2, 1.3

Azure Policy Examples:
• Adaptive network hardening recommendations should be applied on internet facing virtual machines
• All network ports should be restricted on network security groups associated to your virtual machine
• Subnets should be associated with a Network Security Group",ai_analysis,,Validated
autoscale-settings.bicep,DP-4,Data Protection,Enable data at rest encryption by default,HIGH,31.0,"The 'metricResourceUri' property at line 73 references a storage account queue but does not specify or enforce encryption at rest for the storage account. If encryption at rest is not enabled, sensitive queue data could be exposed if the underlying storage is compromised, increasing the blast radius of a breach.","Ensure that the referenced storage account (func_storage_account_id) has encryption at rest enabled using either Microsoft-managed or customer-managed keys. Apply Azure Policy to require encryption at rest for all storage accounts as per Azure Security Benchmark DP-4.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/security/fundamentals/encryption-atrest#encryption-at-rest-in-microsoft-cloud-services) | [Data at rest double encryption](https://docs.microsoft.com/azure/security/fundamentals/encryption-models) | [Azure Disk Encryption](https://docs.microsoft.com/azure/virtual-machines/disk-encryption-overview) | [SQL Transparent Data Encryption](https://docs.microsoft.com/azure/azure-sql/database/transparent-data-encryption-tde-overview)

🔵 Azure Guidance: Many Azure services have data-at-rest encryption enabled by default using service-managed keys. Where not enabled by default enable encryption in Azure services or VMs for storage-level file-level or ...",[Enhanced Implementation Context](https://docs.microsoft.com/azure/security/fundamentals/encryption-atrest#encryption-at-rest-in-microsoft-cloud-services) | [Data at rest double encryption](https://docs.microsoft.com/azure/security/fundamentals/encryption-models) | [Azure Disk Encryption](https://docs.microsoft.com/azure/virtual-machines/disk-encryption-overview) | [SQL Transparent Data Encryption](https://docs.microsoft.com/azure/azure-sql/database/transparent-data-encryption-tde-overview),Many Azure services have data-at-rest encryption enabled by default using service-managed keys. Where not enabled by default enable encryption in Azure services or VMs for storage-level file-level or database-level encryption.,"Enhanced Implementation Context:
• Encryption at rest in Azure: https://docs.microsoft.com/azure/security/fundamentals/encryption-atrest#encryption-at-rest-in-microsoft-cloud-services
• Data at rest double encryption: https://docs.microsoft.com/azure/security/fundamentals/encryption-models
• Encryption model and key management: https://docs.microsoft.com/azure/security/fundamentals/encryption-models
• Azure Disk Encryption: https://docs.microsoft.com/azure/virtual-machines/disk-encryption-overview
• SQL Transparent Data Encryption: https://docs.microsoft.com/azure/azure-sql/database/transparent-data-encryption-tde-overview

Compliance Mappings:
• CIS Controls v8: 3.11
• NIST SP800-53 r4: SC-28
• PCI-DSS v3.2.1: 3.4, 3.5

Azure Policy Examples:
• Virtual machines should encrypt temp disks caches and data flows between Compute and Storage resources
• Transparent Data Encryption on SQL databases should be enabled
• Automation account variables should be encrypted
• Service Fabric clusters should have the ClusterProtectionLevel property set to EncryptAndSign
• Azure Cosmos DB accounts should use customer-managed keys to encrypt data at rest",ai_analysis,,Validated
autoscale-settings.bicep,DP-3,Data Protection,Encrypt sensitive data in transit,CRITICAL,73.0,"The 'metricResourceUri' property at line 73 references a storage account queue ('${func_storage_account_id}/services/queue/queues/file-changes') but does not enforce secure transfer (HTTPS/TLS) for data in transit. If secure transfer is not required on the storage account, attackers could intercept or manipulate data between the autoscale controller and the storage queue, leading to data exposure or unauthorized actions.","Ensure that the referenced storage account (func_storage_account_id) has 'secure transfer required' enabled to enforce HTTPS/TLS for all data in transit. Apply Azure Policy to require secure transfer for all storage accounts and verify that all service connections use encrypted channels as per Azure Security Benchmark DP-3.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/security/fundamentals/double-encryption#data-in-transit) | [Encryption in transit overview](https://docs.microsoft.com/azure/security/fundamentals/encryption-overview#encryption-of-data-in-transit) | [TLS security best practices](https://docs.microsoft.com/security/engineering/solving-tls1-problem) | [Azure Storage secure transfer](https://docs.microsoft.com/azure/storage/common/storage-require-secure-transfer?toc=/azure/storage/blobs/toc.json#require-secure-transfer-for-a-new-storage-account)

🔵 Azure Guidance: Enforce secure transfer in Azure Storage and other services with native encryption. Enforce HTTPS for web applications and TLS v1.2+ for all client connections. Use SSH for Linux and RDP/TLS for Windo...",[Enhanced Implementation Context](https://docs.microsoft.com/azure/security/fundamentals/double-encryption#data-in-transit) | [Encryption in transit overview](https://docs.microsoft.com/azure/security/fundamentals/encryption-overview#encryption-of-data-in-transit) | [TLS security best practices](https://docs.microsoft.com/security/engineering/solving-tls1-problem) | [Azure Storage secure transfer](https://docs.microsoft.com/azure/storage/common/storage-require-secure-transfer?toc=/azure/storage/blobs/toc.json#require-secure-transfer-for-a-new-storage-account),Enforce secure transfer in Azure Storage and other services with native encryption. Enforce HTTPS for web applications and TLS v1.2+ for all client connections. Use SSH for Linux and RDP/TLS for Windows remote management.,"Enhanced Implementation Context:
• Double encryption for Azure data in transit: https://docs.microsoft.com/azure/security/fundamentals/double-encryption#data-in-transit
• Encryption in transit overview: https://docs.microsoft.com/azure/security/fundamentals/encryption-overview#encryption-of-data-in-transit
• TLS security best practices: https://docs.microsoft.com/security/engineering/solving-tls1-problem
• Azure Storage secure transfer: https://docs.microsoft.com/azure/storage/common/storage-require-secure-transfer?toc=/azure/storage/blobs/toc.json#require-secure-transfer-for-a-new-storage-account

Compliance Mappings:
• CIS Controls v8: 3.10
• NIST SP800-53 r4: SC-8
• PCI-DSS v3.2.1: 3.5, 3.6, 4.1

Azure Policy Examples:
• Kubernetes clusters should be accessible only over HTTPS
• Only secure connections to your Azure Cache for Redis should be enabled
• FTPS only should be required in your Function App
• Secure transfer to storage accounts should be enabled
• Function App should only be accessible over HTTPS
• Latest TLS version should be used in your API App
• Web Application should only be accessible over HTTPS
• Enforce SSL connection should be enabled for PostgreSQL database servers
• Latest TLS version should be used in your Web App",ai_analysis,,Validated
autoscale-settings.bicep,DP-2,Data Protection,Monitor anomalies and threats targeting sensitive data,CRITICAL,111.0,"The 'Microsoft.Insights/diagnosticSettings' resource at line 111 enables logging to a Log Analytics workspace but does not specify monitoring or alerting for anomalous data access or exfiltration events. Without active monitoring, unauthorized data transfers or suspicious activities may go undetected, enabling attackers to exfiltrate sensitive data from monitored resources.","Configure Azure Defender for Storage and enable advanced threat protection on all storage accounts and monitored resources. Set up alerts in Azure Security Center and Log Analytics to detect and respond to anomalous data access or exfiltration attempts as per Azure Security Benchmark DP-2.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/azure-sql/database/azure-defender-for-sql) | [Azure Defender for Storage](https://docs.microsoft.com/azure/storage/common/storage-advanced-threat-protection?tabs=azure-security-center) | [Azure Purview data insights](https://docs.microsoft.com/azure/purview/concept-insights) | [Microsoft 365 DLP](https://docs.microsoft.com/microsoft-365/compliance/dlp-learn-about-dlp) | [Azure Information Protection monitoring](https://docs.microsoft.com/azure/information-protection/reports-aip)

🔵 Azure Guidance: Use Azure Information Protection to monitor classified and labeled data. Use Azure Defender for Storage SQL and Cosmos DB to alert on anomalous transfers. Consider Microsoft 365 DLP or host-based DLP ...",[Enhanced Implementation Context](https://docs.microsoft.com/azure/azure-sql/database/azure-defender-for-sql) | [Azure Defender for Storage](https://docs.microsoft.com/azure/storage/common/storage-advanced-threat-protection?tabs=azure-security-center) | [Azure Purview data insights](https://docs.microsoft.com/azure/purview/concept-insights) | [Microsoft 365 DLP](https://docs.microsoft.com/microsoft-365/compliance/dlp-learn-about-dlp) | [Azure Information Protection monitoring](https://docs.microsoft.com/azure/information-protection/reports-aip),Use Azure Information Protection to monitor classified and labeled data. Use Azure Defender for Storage SQL and Cosmos DB to alert on anomalous transfers. Consider Microsoft 365 DLP or host-based DLP solutions for compliance requirements.,"Enhanced Implementation Context:
• Azure Defender for SQL: https://docs.microsoft.com/azure/azure-sql/database/azure-defender-for-sql
• Azure Defender for Storage: https://docs.microsoft.com/azure/storage/common/storage-advanced-threat-protection?tabs=azure-security-center
• Azure Purview data insights: https://docs.microsoft.com/azure/purview/concept-insights
• Microsoft 365 DLP: https://docs.microsoft.com/microsoft-365/compliance/dlp-learn-about-dlp
• Azure Information Protection monitoring: https://docs.microsoft.com/azure/information-protection/reports-aip

Compliance Mappings:
• CIS Controls v8: 3.13
• NIST SP800-53 r4: AC-4, SI-4
• PCI-DSS v3.2.1: A3.2

Azure Policy Examples:
• Azure Defender for open-source relational databases should be enabled
• Azure Defender for Storage should be enabled
• Azure Defender for SQL servers on machines should be enabled
• Azure Defender for Azure SQL Database servers should be enabled
• Azure Defender for SQL should be enabled for unprotected SQL Managed Instances",ai_analysis,,Validated
feature-flags.bicep,DP-4,Data Protection,Enable data at rest encryption by default,CRITICAL,14.0,"The feature flag 'EnableBlobRetentionPolicy' is set to 'false', which disables automatic deletion of expired blobs in Azure Storage. Without a retention policy, sensitive or regulated data may persist beyond its required lifecycle, increasing the risk of data exposure if the storage account is compromised. Attackers gaining access could exfiltrate or manipulate stale data, expanding the blast radius of a breach.","Set 'EnableBlobRetentionPolicy' to 'true' to enforce automatic deletion of expired blobs. Additionally, configure Azure Storage account policies to enable soft delete and legal hold as appropriate. Review and apply Azure Policy 'RequireEncryptionAtRest' and ensure all data at rest is encrypted.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/security/fundamentals/encryption-atrest#encryption-at-rest-in-microsoft-cloud-services) | [Data at rest double encryption](https://docs.microsoft.com/azure/security/fundamentals/encryption-models) | [Azure Disk Encryption](https://docs.microsoft.com/azure/virtual-machines/disk-encryption-overview) | [SQL Transparent Data Encryption](https://docs.microsoft.com/azure/azure-sql/database/transparent-data-encryption-tde-overview)

🔵 Azure Guidance: Many Azure services have data-at-rest encryption enabled by default using service-managed keys. Where not enabled by default enable encryption in Azure services or VMs for storage-level file-level or ...",[Enhanced Implementation Context](https://docs.microsoft.com/azure/security/fundamentals/encryption-atrest#encryption-at-rest-in-microsoft-cloud-services) | [Data at rest double encryption](https://docs.microsoft.com/azure/security/fundamentals/encryption-models) | [Azure Disk Encryption](https://docs.microsoft.com/azure/virtual-machines/disk-encryption-overview) | [SQL Transparent Data Encryption](https://docs.microsoft.com/azure/azure-sql/database/transparent-data-encryption-tde-overview),Many Azure services have data-at-rest encryption enabled by default using service-managed keys. Where not enabled by default enable encryption in Azure services or VMs for storage-level file-level or database-level encryption.,"Enhanced Implementation Context:
• Encryption at rest in Azure: https://docs.microsoft.com/azure/security/fundamentals/encryption-atrest#encryption-at-rest-in-microsoft-cloud-services
• Data at rest double encryption: https://docs.microsoft.com/azure/security/fundamentals/encryption-models
• Encryption model and key management: https://docs.microsoft.com/azure/security/fundamentals/encryption-models
• Azure Disk Encryption: https://docs.microsoft.com/azure/virtual-machines/disk-encryption-overview
• SQL Transparent Data Encryption: https://docs.microsoft.com/azure/azure-sql/database/transparent-data-encryption-tde-overview

Compliance Mappings:
• CIS Controls v8: 3.11
• NIST SP800-53 r4: SC-28
• PCI-DSS v3.2.1: 3.4, 3.5

Azure Policy Examples:
• Virtual machines should encrypt temp disks caches and data flows between Compute and Storage resources
• Transparent Data Encryption on SQL databases should be enabled
• Automation account variables should be encrypted
• Service Fabric clusters should have the ClusterProtectionLevel property set to EncryptAndSign
• Azure Cosmos DB accounts should use customer-managed keys to encrypt data at rest",ai_analysis,,Validated
feature-flags.bicep,DP-2,Data Protection,Monitor anomalies and threats targeting sensitive data,CRITICAL,38.0,"The feature flag 'EnableGenevaAuditing' is set to 'false', disabling Geneva auditing for the application. Without auditing, anomalous or unauthorized access to ServiceBus or Storage resources may go undetected, enabling attackers to exfiltrate or manipulate sensitive data without detection. This compromises monitoring and increases the risk of undetected data breaches.","Set 'EnableGenevaAuditing' to 'true' to enable comprehensive auditing and monitoring. Ensure that audit logs are sent to a secure, immutable location and integrated with SIEM solutions for real-time alerting. Apply Azure Defender for Storage and ServiceBus for advanced threat detection.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/azure-sql/database/azure-defender-for-sql) | [Azure Defender for Storage](https://docs.microsoft.com/azure/storage/common/storage-advanced-threat-protection?tabs=azure-security-center) | [Azure Purview data insights](https://docs.microsoft.com/azure/purview/concept-insights) | [Microsoft 365 DLP](https://docs.microsoft.com/microsoft-365/compliance/dlp-learn-about-dlp) | [Azure Information Protection monitoring](https://docs.microsoft.com/azure/information-protection/reports-aip)

🔵 Azure Guidance: Use Azure Information Protection to monitor classified and labeled data. Use Azure Defender for Storage SQL and Cosmos DB to alert on anomalous transfers. Consider Microsoft 365 DLP or host-based DLP ...",[Enhanced Implementation Context](https://docs.microsoft.com/azure/azure-sql/database/azure-defender-for-sql) | [Azure Defender for Storage](https://docs.microsoft.com/azure/storage/common/storage-advanced-threat-protection?tabs=azure-security-center) | [Azure Purview data insights](https://docs.microsoft.com/azure/purview/concept-insights) | [Microsoft 365 DLP](https://docs.microsoft.com/microsoft-365/compliance/dlp-learn-about-dlp) | [Azure Information Protection monitoring](https://docs.microsoft.com/azure/information-protection/reports-aip),Use Azure Information Protection to monitor classified and labeled data. Use Azure Defender for Storage SQL and Cosmos DB to alert on anomalous transfers. Consider Microsoft 365 DLP or host-based DLP solutions for compliance requirements.,"Enhanced Implementation Context:
• Azure Defender for SQL: https://docs.microsoft.com/azure/azure-sql/database/azure-defender-for-sql
• Azure Defender for Storage: https://docs.microsoft.com/azure/storage/common/storage-advanced-threat-protection?tabs=azure-security-center
• Azure Purview data insights: https://docs.microsoft.com/azure/purview/concept-insights
• Microsoft 365 DLP: https://docs.microsoft.com/microsoft-365/compliance/dlp-learn-about-dlp
• Azure Information Protection monitoring: https://docs.microsoft.com/azure/information-protection/reports-aip

Compliance Mappings:
• CIS Controls v8: 3.13
• NIST SP800-53 r4: AC-4, SI-4
• PCI-DSS v3.2.1: A3.2

Azure Policy Examples:
• Azure Defender for open-source relational databases should be enabled
• Azure Defender for Storage should be enabled
• Azure Defender for SQL servers on machines should be enabled
• Azure Defender for Azure SQL Database servers should be enabled
• Azure Defender for SQL should be enabled for unprotected SQL Managed Instances",ai_analysis,,Validated
function.bicep,IM-8,Identity Management,Restrict the exposure of credential and secrets,CRITICAL,7.0,"The parameter 'client_id' (line 7) is defined as a string and may be used to pass sensitive credentials or secrets directly through deployment parameters. If secrets or credentials are provided via parameters instead of secure mechanisms like Azure Key Vault, attackers with access to deployment logs, templates, or parameter files could exfiltrate these secrets, leading to full compromise of application identities and lateral movement across Azure resources.","Do not pass secrets, client secrets, or sensitive credentials via parameters. Store all secrets in Azure Key Vault and reference them securely using managed identities. Update the deployment process to retrieve sensitive values from Key Vault at runtime, and ensure 'client_id' is not used for secret material. Reference: ASB IM-8.

📚 References: [Enhanced Implementation Context](https://secdevtools.azurewebsites.net/helpcredscan.html) | [GitHub secret scanning](https://docs.github.com/github/administering-a-repository/about-secret-scanning) | [Azure Key Vault developer guide](https://docs.microsoft.com/azure/key-vault/general/developers-guide) | [Secure coding practices](https://docs.microsoft.com/azure/security/develop/secure-dev-overview) | [Application security guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-application-security-devsecops)

🔵 Azure Guidance: Store secrets in Azure Key Vault instead of embedding in code. Implement Azure DevOps Credential Scanner or GitHub secret scanning. Use managed identities for Azure Key Vault access.",[Enhanced Implementation Context](https://secdevtools.azurewebsites.net/helpcredscan.html) | [GitHub secret scanning](https://docs.github.com/github/administering-a-repository/about-secret-scanning) | [Azure Key Vault developer guide](https://docs.microsoft.com/azure/key-vault/general/developers-guide) | [Secure coding practices](https://docs.microsoft.com/azure/security/develop/secure-dev-overview) | [Application security guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-application-security-devsecops),Store secrets in Azure Key Vault instead of embedding in code. Implement Azure DevOps Credential Scanner or GitHub secret scanning. Use managed identities for Azure Key Vault access.,"Enhanced Implementation Context:
• Azure DevOps Credential Scanner setup: https://secdevtools.azurewebsites.net/helpcredscan.html
• GitHub secret scanning: https://docs.github.com/github/administering-a-repository/about-secret-scanning
• Azure Key Vault developer guide: https://docs.microsoft.com/azure/key-vault/general/developers-guide
• Secure coding practices: https://docs.microsoft.com/azure/security/develop/secure-dev-overview
• Application security guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-application-security-devsecops

Compliance Mappings:
• CIS Controls v8: 16.9, 16.12
• NIST SP800-53 r4: IA-5
• PCI-DSS v3.2.1: 3.5, 6.3, 8.2

Azure Policy Examples:
• No applicable built-in policy (requires development process implementation)
• Implement credential scanning in CI/CD pipelines
• Enforce Azure Key Vault usage for secret storage
• Monitor and audit secret access patterns
• Require managed identities for Azure service authentication",ai_analysis,,Validated
function.bicep,IM-8,Identity Management,Restrict the exposure of credential and secrets,CRITICAL,14.0,"The parameter 'signedExpiry' (line 14) is defined as a string and may be used to pass sensitive tokens, signed URLs, or time-limited credentials. Passing such sensitive values as parameters exposes them to deployment logs, template repositories, and anyone with access to the deployment pipeline, enabling attackers to gain unauthorized access to protected resources or escalate privileges.","Store all sensitive tokens, signed URLs, or time-limited credentials in Azure Key Vault and reference them securely using managed identities. Do not pass such values as parameters. Update the deployment process to retrieve these values from Key Vault at runtime. Reference: ASB IM-8.

📚 References: [Enhanced Implementation Context](https://secdevtools.azurewebsites.net/helpcredscan.html) | [GitHub secret scanning](https://docs.github.com/github/administering-a-repository/about-secret-scanning) | [Azure Key Vault developer guide](https://docs.microsoft.com/azure/key-vault/general/developers-guide) | [Secure coding practices](https://docs.microsoft.com/azure/security/develop/secure-dev-overview) | [Application security guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-application-security-devsecops)

🔵 Azure Guidance: Store secrets in Azure Key Vault instead of embedding in code. Implement Azure DevOps Credential Scanner or GitHub secret scanning. Use managed identities for Azure Key Vault access.",[Enhanced Implementation Context](https://secdevtools.azurewebsites.net/helpcredscan.html) | [GitHub secret scanning](https://docs.github.com/github/administering-a-repository/about-secret-scanning) | [Azure Key Vault developer guide](https://docs.microsoft.com/azure/key-vault/general/developers-guide) | [Secure coding practices](https://docs.microsoft.com/azure/security/develop/secure-dev-overview) | [Application security guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-application-security-devsecops),Store secrets in Azure Key Vault instead of embedding in code. Implement Azure DevOps Credential Scanner or GitHub secret scanning. Use managed identities for Azure Key Vault access.,"Enhanced Implementation Context:
• Azure DevOps Credential Scanner setup: https://secdevtools.azurewebsites.net/helpcredscan.html
• GitHub secret scanning: https://docs.github.com/github/administering-a-repository/about-secret-scanning
• Azure Key Vault developer guide: https://docs.microsoft.com/azure/key-vault/general/developers-guide
• Secure coding practices: https://docs.microsoft.com/azure/security/develop/secure-dev-overview
• Application security guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-application-security-devsecops

Compliance Mappings:
• CIS Controls v8: 16.9, 16.12
• NIST SP800-53 r4: IA-5
• PCI-DSS v3.2.1: 3.5, 6.3, 8.2

Azure Policy Examples:
• No applicable built-in policy (requires development process implementation)
• Implement credential scanning in CI/CD pipelines
• Enforce Azure Key Vault usage for secret storage
• Monitor and audit secret access patterns
• Require managed identities for Azure service authentication",ai_analysis,,Validated
function.bicep,IM-8,Identity Management,Restrict the exposure of credential and secrets,CRITICAL,33.0,"The variable 'storage_account_sas' (line 33) is used to construct a SAS (Shared Access Signature) token configuration. If any secret, key, or sensitive value (such as a SAS token or storage key) is directly embedded or constructed in code or variables, it can be inadvertently exposed through source control, logs, or deployment artifacts. Attackers who gain access to this variable could use the SAS token to access, modify, or delete storage account data, resulting in data exfiltration or destruction. The blast radius includes full compromise of the associated storage account resources, depending on the permissions granted by the SAS.","Store all sensitive values, such as SAS tokens, storage account keys, or secrets, in Azure Key Vault. Reference secrets securely at deployment time using managed identities. Remove any direct construction or embedding of sensitive values in variables. Implement Azure DevOps Credential Scanner or GitHub secret scanning to detect accidental secret exposure. Reference: ASB IM-8.

📚 References: [Enhanced Implementation Context](https://secdevtools.azurewebsites.net/helpcredscan.html) | [GitHub secret scanning](https://docs.github.com/github/administering-a-repository/about-secret-scanning) | [Azure Key Vault developer guide](https://docs.microsoft.com/azure/key-vault/general/developers-guide) | [Secure coding practices](https://docs.microsoft.com/azure/security/develop/secure-dev-overview) | [Application security guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-application-security-devsecops)

🔵 Azure Guidance: Store secrets in Azure Key Vault instead of embedding in code. Implement Azure DevOps Credential Scanner or GitHub secret scanning. Use managed identities for Azure Key Vault access.",[Enhanced Implementation Context](https://secdevtools.azurewebsites.net/helpcredscan.html) | [GitHub secret scanning](https://docs.github.com/github/administering-a-repository/about-secret-scanning) | [Azure Key Vault developer guide](https://docs.microsoft.com/azure/key-vault/general/developers-guide) | [Secure coding practices](https://docs.microsoft.com/azure/security/develop/secure-dev-overview) | [Application security guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-application-security-devsecops),Store secrets in Azure Key Vault instead of embedding in code. Implement Azure DevOps Credential Scanner or GitHub secret scanning. Use managed identities for Azure Key Vault access.,"Enhanced Implementation Context:
• Azure DevOps Credential Scanner setup: https://secdevtools.azurewebsites.net/helpcredscan.html
• GitHub secret scanning: https://docs.github.com/github/administering-a-repository/about-secret-scanning
• Azure Key Vault developer guide: https://docs.microsoft.com/azure/key-vault/general/developers-guide
• Secure coding practices: https://docs.microsoft.com/azure/security/develop/secure-dev-overview
• Application security guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-application-security-devsecops

Compliance Mappings:
• CIS Controls v8: 16.9, 16.12
• NIST SP800-53 r4: IA-5
• PCI-DSS v3.2.1: 3.5, 6.3, 8.2

Azure Policy Examples:
• No applicable built-in policy (requires development process implementation)
• Implement credential scanning in CI/CD pipelines
• Enforce Azure Key Vault usage for secret storage
• Monitor and audit secret access patterns
• Require managed identities for Azure service authentication",ai_analysis,,Validated
function.bicep,DP-3,Data Protection,Encrypt sensitive data in transit,HIGH,33.0,"The 'signedPermission' property in the 'storage_account_sas' variable (line 35) is set to 'rwdlacup', which grants read, write, delete, list, add, create, update, and process permissions. If this SAS token is not restricted by IP, protocol, or resource type, and is not enforced to use HTTPS only, it could allow attackers to access or modify storage data over insecure channels, leading to data-in-transit exposure or man-in-the-middle attacks. The blast radius includes unauthorized data access or modification if the SAS token is leaked.","Ensure that SAS tokens are generated with 'httpsOnly' enforced and restrict permissions to the minimum required. Limit the allowed IP range and set a short expiry. Always enforce secure transfer (HTTPS) for storage accounts. Reference: ASB DP-3.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/security/fundamentals/double-encryption#data-in-transit) | [Encryption in transit overview](https://docs.microsoft.com/azure/security/fundamentals/encryption-overview#encryption-of-data-in-transit) | [TLS security best practices](https://docs.microsoft.com/security/engineering/solving-tls1-problem) | [Azure Storage secure transfer](https://docs.microsoft.com/azure/storage/common/storage-require-secure-transfer?toc=/azure/storage/blobs/toc.json#require-secure-transfer-for-a-new-storage-account)

🔵 Azure Guidance: Enforce secure transfer in Azure Storage and other services with native encryption. Enforce HTTPS for web applications and TLS v1.2+ for all client connections. Use SSH for Linux and RDP/TLS for Windo...",[Enhanced Implementation Context](https://docs.microsoft.com/azure/security/fundamentals/double-encryption#data-in-transit) | [Encryption in transit overview](https://docs.microsoft.com/azure/security/fundamentals/encryption-overview#encryption-of-data-in-transit) | [TLS security best practices](https://docs.microsoft.com/security/engineering/solving-tls1-problem) | [Azure Storage secure transfer](https://docs.microsoft.com/azure/storage/common/storage-require-secure-transfer?toc=/azure/storage/blobs/toc.json#require-secure-transfer-for-a-new-storage-account),Enforce secure transfer in Azure Storage and other services with native encryption. Enforce HTTPS for web applications and TLS v1.2+ for all client connections. Use SSH for Linux and RDP/TLS for Windows remote management.,"Enhanced Implementation Context:
• Double encryption for Azure data in transit: https://docs.microsoft.com/azure/security/fundamentals/double-encryption#data-in-transit
• Encryption in transit overview: https://docs.microsoft.com/azure/security/fundamentals/encryption-overview#encryption-of-data-in-transit
• TLS security best practices: https://docs.microsoft.com/security/engineering/solving-tls1-problem
• Azure Storage secure transfer: https://docs.microsoft.com/azure/storage/common/storage-require-secure-transfer?toc=/azure/storage/blobs/toc.json#require-secure-transfer-for-a-new-storage-account

Compliance Mappings:
• CIS Controls v8: 3.10
• NIST SP800-53 r4: SC-8
• PCI-DSS v3.2.1: 3.5, 3.6, 4.1

Azure Policy Examples:
• Kubernetes clusters should be accessible only over HTTPS
• Only secure connections to your Azure Cache for Redis should be enabled
• FTPS only should be required in your Function App
• Secure transfer to storage accounts should be enabled
• Function App should only be accessible over HTTPS
• Latest TLS version should be used in your API App
• Web Application should only be accessible over HTTPS
• Enforce SSL connection should be enabled for PostgreSQL database servers
• Latest TLS version should be used in your Web App",ai_analysis,,Validated
function.bicep,IM-8,Identity Management,Restrict the exposure of credential and secrets,HIGH,58.0,The variable 'remoteDebuggingEnabled' is set to true in 'extraProperties' (line 58). Enabling remote debugging in production environments can expose sensitive application internals and provide an attack vector for privilege escalation or lateral movement if the debugging endpoint is not properly secured. Attackers could exploit this to gain code execution or access sensitive data within the App Service.,"Disable remote debugging in production environments. If remote debugging is required for troubleshooting, restrict access to trusted IPs, use strong authentication, and ensure it is disabled immediately after use. Reference: ASB IM-8.

📚 References: [Enhanced Implementation Context](https://secdevtools.azurewebsites.net/helpcredscan.html) | [GitHub secret scanning](https://docs.github.com/github/administering-a-repository/about-secret-scanning) | [Azure Key Vault developer guide](https://docs.microsoft.com/azure/key-vault/general/developers-guide) | [Secure coding practices](https://docs.microsoft.com/azure/security/develop/secure-dev-overview) | [Application security guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-application-security-devsecops)

🔵 Azure Guidance: Store secrets in Azure Key Vault instead of embedding in code. Implement Azure DevOps Credential Scanner or GitHub secret scanning. Use managed identities for Azure Key Vault access.",[Enhanced Implementation Context](https://secdevtools.azurewebsites.net/helpcredscan.html) | [GitHub secret scanning](https://docs.github.com/github/administering-a-repository/about-secret-scanning) | [Azure Key Vault developer guide](https://docs.microsoft.com/azure/key-vault/general/developers-guide) | [Secure coding practices](https://docs.microsoft.com/azure/security/develop/secure-dev-overview) | [Application security guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-application-security-devsecops),Store secrets in Azure Key Vault instead of embedding in code. Implement Azure DevOps Credential Scanner or GitHub secret scanning. Use managed identities for Azure Key Vault access.,"Enhanced Implementation Context:
• Azure DevOps Credential Scanner setup: https://secdevtools.azurewebsites.net/helpcredscan.html
• GitHub secret scanning: https://docs.github.com/github/administering-a-repository/about-secret-scanning
• Azure Key Vault developer guide: https://docs.microsoft.com/azure/key-vault/general/developers-guide
• Secure coding practices: https://docs.microsoft.com/azure/security/develop/secure-dev-overview
• Application security guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-application-security-devsecops

Compliance Mappings:
• CIS Controls v8: 16.9, 16.12
• NIST SP800-53 r4: IA-5
• PCI-DSS v3.2.1: 3.5, 6.3, 8.2

Azure Policy Examples:
• No applicable built-in policy (requires development process implementation)
• Implement credential scanning in CI/CD pipelines
• Enforce Azure Key Vault usage for secret storage
• Monitor and audit secret access patterns
• Require managed identities for Azure service authentication",ai_analysis,,Validated
function.bicep,NS-8,Network Security,Detect and disable insecure services and protocols,HIGH,71.0,"The App Service resource does not specify the minimum TLS version for client connections. If the default is not explicitly set to TLS 1.2 or higher, the app may accept insecure protocols (e.g., TLS 1.0/1.1), enabling downgrade attacks and interception of sensitive data in transit.","Explicitly set the 'minTlsVersion' property in the App Service 'siteConfig' to '1.2' or higher. Example: siteConfig: { minTlsVersion: '1.2' }.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/sentinel/quickstart-get-visibility#use-built-in-workbooks) | [TLS security best practices](https://docs.microsoft.com/security/engineering/solving-tls1-problem) | [Secure protocol configuration](https://docs.microsoft.com/azure/security/fundamentals/network-best-practices) | [Network security monitoring](https://docs.microsoft.com/azure/security/fundamentals/network-monitoring) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture)

🔵 Azure Guidance: Use Azure Sentinel's Insecure Protocol Workbook to discover insecure services like SSL/TLSv1 SSHv1 SMBv1 LM/NTLMv1. Disable insecure protocols or use compensating controls like NSG/Firewall blocking.",[Enhanced Implementation Context](https://docs.microsoft.com/azure/sentinel/quickstart-get-visibility#use-built-in-workbooks) | [TLS security best practices](https://docs.microsoft.com/security/engineering/solving-tls1-problem) | [Secure protocol configuration](https://docs.microsoft.com/azure/security/fundamentals/network-best-practices) | [Network security monitoring](https://docs.microsoft.com/azure/security/fundamentals/network-monitoring) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture),Use Azure Sentinel's Insecure Protocol Workbook to discover insecure services like SSL/TLSv1 SSHv1 SMBv1 LM/NTLMv1. Disable insecure protocols or use compensating controls like NSG/Firewall blocking.,"Enhanced Implementation Context:
• Azure Sentinel insecure protocols workbook: https://docs.microsoft.com/azure/sentinel/quickstart-get-visibility#use-built-in-workbooks
• TLS security best practices: https://docs.microsoft.com/security/engineering/solving-tls1-problem
• Secure protocol configuration: https://docs.microsoft.com/azure/security/fundamentals/network-best-practices
• Network security monitoring: https://docs.microsoft.com/azure/security/fundamentals/network-monitoring
• Security architecture guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture

Compliance Mappings:
• CIS Controls v8: 4.4, 4.8
• NIST SP800-53 r4: CM-2, CM-6, CM-7
• PCI-DSS v3.2.1: 4.1, A2.1, A2.2, A2.3

Azure Policy Examples:
• Latest TLS version should be used in your API App
• Latest TLS version should be used in your Web App
• Latest TLS version should be used in your Function App
• Secure transfer to storage accounts should be enabled",ai_analysis,,Validated
function.bicep,NS-1,Network Security,Establish network segmentation boundaries,HIGH,90.0,"The 'virtualNetworkSubnetId' property is set for the App Service (Function App) resource, but there is no evidence in this chunk that a Network Security Group (NSG) is associated with the subnet. Without an NSG, the subnet may be exposed to unrestricted network traffic, enabling lateral movement or direct access to the function app from untrusted sources. This increases the blast radius in the event of a compromise.","Ensure that the subnet referenced by 'hubSubnetId' has a Network Security Group (NSG) associated with it, with rules that restrict inbound and outbound traffic to only required sources and destinations. Example: Associate an NSG with the subnet and define deny-by-default rules, only allowing necessary ports and IP ranges.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/virtual-network/concepts-and-best-practices) | [Subnet management](https://docs.microsoft.com/azure/virtual-network/virtual-network-manage-subnet) | [NSG security rules](https://docs.microsoft.com/azure/virtual-network/tutorial-filter-network-traffic) | [Application security groups](https://docs.microsoft.com/azure/virtual-network/network-security-groups-overview#application-security-groups) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture)

🔵 Azure Guidance: Create virtual networks (VNets) as fundamental segmentation. Use network security groups (NSGs) and application security groups (ASGs) for traffic control. Implement deny-by-default approach.",[Enhanced Implementation Context](https://docs.microsoft.com/azure/virtual-network/concepts-and-best-practices) | [Subnet management](https://docs.microsoft.com/azure/virtual-network/virtual-network-manage-subnet) | [NSG security rules](https://docs.microsoft.com/azure/virtual-network/tutorial-filter-network-traffic) | [Application security groups](https://docs.microsoft.com/azure/virtual-network/network-security-groups-overview#application-security-groups) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture),Create virtual networks (VNets) as fundamental segmentation. Use network security groups (NSGs) and application security groups (ASGs) for traffic control. Implement deny-by-default approach.,"Enhanced Implementation Context:
• Virtual network concepts: https://docs.microsoft.com/azure/virtual-network/concepts-and-best-practices
• Subnet management: https://docs.microsoft.com/azure/virtual-network/virtual-network-manage-subnet
• NSG security rules: https://docs.microsoft.com/azure/virtual-network/tutorial-filter-network-traffic
• Application security groups: https://docs.microsoft.com/azure/virtual-network/network-security-groups-overview#application-security-groups
• Security architecture guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture

Compliance Mappings:
• CIS Controls v8: 3.12, 13.4, 4.4
• NIST SP800-53 r4: AC-4, SC-2, SC-7
• PCI-DSS v3.2.1: 1.1, 1.2, 1.3

Azure Policy Examples:
• Adaptive network hardening recommendations should be applied on internet facing virtual machines
• All network ports should be restricted on network security groups associated to your virtual machine
• Subnets should be associated with a Network Security Group",ai_analysis,,Validated
function.bicep,NS-2,Network Security,Secure cloud services with network controls,HIGH,90.0,"The 'virtualNetworkSubnetId' property integrates the App Service with a VNet, but there is no evidence of private endpoints or explicit disabling of public network access for the App Service. Without private endpoints and with public access enabled, attackers could reach the function app from the public internet, increasing the risk of initial access and data exposure.","Configure the App Service to use private endpoints for all inbound and outbound connections, and explicitly disable public network access. In Bicep, set 'publicNetworkAccess' to 'Disabled' for the App Service resource, and deploy a private endpoint resource for the function app.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/private-link/private-link-overview) | [Private endpoints for Storage](https://docs.microsoft.com/azure/storage/common/storage-private-endpoints) | [SQL private endpoints](https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview) | [Key Vault private endpoints](https://docs.microsoft.com/azure/key-vault/general/private-link-service) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture)

🔵 Azure Guidance: Deploy private endpoints for Azure resources supporting Private Link. Disable public network access where feasible. Use VNet integration for services requiring private access.",[Enhanced Implementation Context](https://docs.microsoft.com/azure/private-link/private-link-overview) | [Private endpoints for Storage](https://docs.microsoft.com/azure/storage/common/storage-private-endpoints) | [SQL private endpoints](https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview) | [Key Vault private endpoints](https://docs.microsoft.com/azure/key-vault/general/private-link-service) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture),Deploy private endpoints for Azure resources supporting Private Link. Disable public network access where feasible. Use VNet integration for services requiring private access.,"Enhanced Implementation Context:
• Private Link overview: https://docs.microsoft.com/azure/private-link/private-link-overview
• Private endpoints for Storage: https://docs.microsoft.com/azure/storage/common/storage-private-endpoints
• SQL private endpoints: https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview
• Key Vault private endpoints: https://docs.microsoft.com/azure/key-vault/general/private-link-service
• Security architecture guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture

Compliance Mappings:
• CIS Controls v8: 3.12, 4.4
• NIST SP800-53 r4: AC-4, SC-2, SC-7
• PCI-DSS v3.2.1: 1.1, 1.2, 1.3

Azure Policy Examples:
• Private endpoint should be enabled for PostgreSQL/MySQL/MariaDB servers
• Storage accounts should use private link
• Azure SQL Database should disable public network access
• Cognitive Services accounts should restrict network access
• Container registries should use private link",ai_analysis,,Validated
function.bicep,DP-3,Data Protection,Encrypt sensitive data in transit,CRITICAL,141.0,"The 'sasUrl' property on line 141 exposes Azure Blob Storage logs via a SAS token. If the SAS token is generated with overly broad permissions or excessive duration, attackers who obtain the URL can exfiltrate or tamper with sensitive application logs. This creates a direct data-in-transit exposure vector, especially if HTTPS is not strictly enforced for the storage endpoint, increasing the blast radius to all log data in the specified container.","Ensure that the SAS token used in 'sasUrl' is generated with the minimum required permissions (read-only if possible), a short expiration time, and is only accessible over HTTPS. Enforce 'secure transfer required' on the associated storage account and validate that all access to the blob endpoint is restricted to HTTPS. Regularly rotate SAS tokens and monitor their usage. Reference: Azure Security Benchmark DP-3.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/security/fundamentals/double-encryption#data-in-transit) | [Encryption in transit overview](https://docs.microsoft.com/azure/security/fundamentals/encryption-overview#encryption-of-data-in-transit) | [TLS security best practices](https://docs.microsoft.com/security/engineering/solving-tls1-problem) | [Azure Storage secure transfer](https://docs.microsoft.com/azure/storage/common/storage-require-secure-transfer?toc=/azure/storage/blobs/toc.json#require-secure-transfer-for-a-new-storage-account)

🔵 Azure Guidance: Enforce secure transfer in Azure Storage and other services with native encryption. Enforce HTTPS for web applications and TLS v1.2+ for all client connections. Use SSH for Linux and RDP/TLS for Windo...",[Enhanced Implementation Context](https://docs.microsoft.com/azure/security/fundamentals/double-encryption#data-in-transit) | [Encryption in transit overview](https://docs.microsoft.com/azure/security/fundamentals/encryption-overview#encryption-of-data-in-transit) | [TLS security best practices](https://docs.microsoft.com/security/engineering/solving-tls1-problem) | [Azure Storage secure transfer](https://docs.microsoft.com/azure/storage/common/storage-require-secure-transfer?toc=/azure/storage/blobs/toc.json#require-secure-transfer-for-a-new-storage-account),Enforce secure transfer in Azure Storage and other services with native encryption. Enforce HTTPS for web applications and TLS v1.2+ for all client connections. Use SSH for Linux and RDP/TLS for Windows remote management.,"Enhanced Implementation Context:
• Double encryption for Azure data in transit: https://docs.microsoft.com/azure/security/fundamentals/double-encryption#data-in-transit
• Encryption in transit overview: https://docs.microsoft.com/azure/security/fundamentals/encryption-overview#encryption-of-data-in-transit
• TLS security best practices: https://docs.microsoft.com/security/engineering/solving-tls1-problem
• Azure Storage secure transfer: https://docs.microsoft.com/azure/storage/common/storage-require-secure-transfer?toc=/azure/storage/blobs/toc.json#require-secure-transfer-for-a-new-storage-account

Compliance Mappings:
• CIS Controls v8: 3.10
• NIST SP800-53 r4: SC-8
• PCI-DSS v3.2.1: 3.5, 3.6, 4.1

Azure Policy Examples:
• Kubernetes clusters should be accessible only over HTTPS
• Only secure connections to your Azure Cache for Redis should be enabled
• FTPS only should be required in your Function App
• Secure transfer to storage accounts should be enabled
• Function App should only be accessible over HTTPS
• Latest TLS version should be used in your API App
• Web Application should only be accessible over HTTPS
• Enforce SSL connection should be enabled for PostgreSQL database servers
• Latest TLS version should be used in your Web App",ai_analysis,,Validated
function.bicep,DP-1,Data Protection,Discover classify and label sensitive data,CRITICAL,148.0,"The output 'principalId' exposes the managed identity principalId of the Azure Function. Attackers who gain access to deployment outputs or logs can use this identifier to enumerate or target the managed identity, potentially enabling privilege escalation or lateral movement if the identity is over-privileged. The blast radius includes any resources accessible by this managed identity.","Restrict outputs containing sensitive identity information such as principalId. Only expose such outputs to trusted deployment automation or monitoring systems with strict access controls. Consider removing this output or masking it unless absolutely required for downstream automation. Reference: ASB DP-1.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/cloud-adoption-framework/govern/policy-compliance/data-classification) | [Azure Purview sensitivity labels](https://docs.microsoft.com/azure/purview/create-sensitivity-label) | [Azure Information Protection](https://docs.microsoft.com/azure/information-protection/what-is-information-protection) | [Azure SQL Data Discovery](https://docs.microsoft.com/azure/sql-database/sql-database-data-discovery-and-classification) | [Azure Purview data sources](https://docs.microsoft.com/azure/purview/purview-connector-overview#purview-data-sources)

🔵 Azure Guidance: Use Azure Purview Azure Information Protection and Azure SQL Data Discovery and Classification to centrally scan classify and label sensitive data across Azure on-premises Microsoft 365 and other loca...",[Enhanced Implementation Context](https://docs.microsoft.com/azure/cloud-adoption-framework/govern/policy-compliance/data-classification) | [Azure Purview sensitivity labels](https://docs.microsoft.com/azure/purview/create-sensitivity-label) | [Azure Information Protection](https://docs.microsoft.com/azure/information-protection/what-is-information-protection) | [Azure SQL Data Discovery](https://docs.microsoft.com/azure/sql-database/sql-database-data-discovery-and-classification) | [Azure Purview data sources](https://docs.microsoft.com/azure/purview/purview-connector-overview#purview-data-sources),Use Azure Purview Azure Information Protection and Azure SQL Data Discovery and Classification to centrally scan classify and label sensitive data across Azure on-premises Microsoft 365 and other locations.,"Enhanced Implementation Context:
• Data classification overview: https://docs.microsoft.com/azure/cloud-adoption-framework/govern/policy-compliance/data-classification
• Azure Purview sensitivity labels: https://docs.microsoft.com/azure/purview/create-sensitivity-label
• Azure Information Protection: https://docs.microsoft.com/azure/information-protection/what-is-information-protection
• Azure SQL Data Discovery: https://docs.microsoft.com/azure/sql-database/sql-database-data-discovery-and-classification
• Azure Purview data sources: https://docs.microsoft.com/azure/purview/purview-connector-overview#purview-data-sources

Compliance Mappings:
• CIS Controls v8: 3.2, 3.7, 3.13
• NIST SP800-53 r4: RA-2, SC-28
• PCI-DSS v3.2.1: A3.2

Azure Policy Examples:
• Sensitive data in your SQL databases should be classified
• Implement data discovery and classification across all data stores
• Deploy Azure Purview for enterprise data governance
• Configure sensitivity labels for all data assets",ai_analysis,,Validated
function.bicep,DP-1,Data Protection,Discover classify and label sensitive data,CRITICAL,149.0,"The output 'userAssignedMsiClientId' exposes the clientId of a user-assigned managed identity. Disclosure of this identifier can facilitate targeted attacks, such as identity enumeration or abuse in other Azure tenants, increasing the risk of unauthorized access or privilege escalation.","Limit exposure of managed identity clientId in outputs. Only output this value if required for secure automation, and ensure output access is tightly controlled. Remove or mask this output if not strictly necessary. Reference: ASB DP-1.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/cloud-adoption-framework/govern/policy-compliance/data-classification) | [Azure Purview sensitivity labels](https://docs.microsoft.com/azure/purview/create-sensitivity-label) | [Azure Information Protection](https://docs.microsoft.com/azure/information-protection/what-is-information-protection) | [Azure SQL Data Discovery](https://docs.microsoft.com/azure/sql-database/sql-database-data-discovery-and-classification) | [Azure Purview data sources](https://docs.microsoft.com/azure/purview/purview-connector-overview#purview-data-sources)

🔵 Azure Guidance: Use Azure Purview Azure Information Protection and Azure SQL Data Discovery and Classification to centrally scan classify and label sensitive data across Azure on-premises Microsoft 365 and other loca...",[Enhanced Implementation Context](https://docs.microsoft.com/azure/cloud-adoption-framework/govern/policy-compliance/data-classification) | [Azure Purview sensitivity labels](https://docs.microsoft.com/azure/purview/create-sensitivity-label) | [Azure Information Protection](https://docs.microsoft.com/azure/information-protection/what-is-information-protection) | [Azure SQL Data Discovery](https://docs.microsoft.com/azure/sql-database/sql-database-data-discovery-and-classification) | [Azure Purview data sources](https://docs.microsoft.com/azure/purview/purview-connector-overview#purview-data-sources),Use Azure Purview Azure Information Protection and Azure SQL Data Discovery and Classification to centrally scan classify and label sensitive data across Azure on-premises Microsoft 365 and other locations.,"Enhanced Implementation Context:
• Data classification overview: https://docs.microsoft.com/azure/cloud-adoption-framework/govern/policy-compliance/data-classification
• Azure Purview sensitivity labels: https://docs.microsoft.com/azure/purview/create-sensitivity-label
• Azure Information Protection: https://docs.microsoft.com/azure/information-protection/what-is-information-protection
• Azure SQL Data Discovery: https://docs.microsoft.com/azure/sql-database/sql-database-data-discovery-and-classification
• Azure Purview data sources: https://docs.microsoft.com/azure/purview/purview-connector-overview#purview-data-sources

Compliance Mappings:
• CIS Controls v8: 3.2, 3.7, 3.13
• NIST SP800-53 r4: RA-2, SC-28
• PCI-DSS v3.2.1: A3.2

Azure Policy Examples:
• Sensitive data in your SQL databases should be classified
• Implement data discovery and classification across all data stores
• Deploy Azure Purview for enterprise data governance
• Configure sensitivity labels for all data assets",ai_analysis,,Validated
function.bicep,DP-1,Data Protection,Discover classify and label sensitive data,CRITICAL,150.0,"The output 'userAssignedMsiObjectId' exposes the objectId (principalId) of a user-assigned managed identity. Attackers with access to this value can attempt to enumerate permissions or target the identity for privilege escalation, especially if the identity is assigned broad roles.","Do not output sensitive identity objectIds unless required for secure automation. Restrict access to deployment outputs and consider removing or masking this output. Review managed identity permissions to ensure least privilege. Reference: ASB DP-1.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/cloud-adoption-framework/govern/policy-compliance/data-classification) | [Azure Purview sensitivity labels](https://docs.microsoft.com/azure/purview/create-sensitivity-label) | [Azure Information Protection](https://docs.microsoft.com/azure/information-protection/what-is-information-protection) | [Azure SQL Data Discovery](https://docs.microsoft.com/azure/sql-database/sql-database-data-discovery-and-classification) | [Azure Purview data sources](https://docs.microsoft.com/azure/purview/purview-connector-overview#purview-data-sources)

🔵 Azure Guidance: Use Azure Purview Azure Information Protection and Azure SQL Data Discovery and Classification to centrally scan classify and label sensitive data across Azure on-premises Microsoft 365 and other loca...",[Enhanced Implementation Context](https://docs.microsoft.com/azure/cloud-adoption-framework/govern/policy-compliance/data-classification) | [Azure Purview sensitivity labels](https://docs.microsoft.com/azure/purview/create-sensitivity-label) | [Azure Information Protection](https://docs.microsoft.com/azure/information-protection/what-is-information-protection) | [Azure SQL Data Discovery](https://docs.microsoft.com/azure/sql-database/sql-database-data-discovery-and-classification) | [Azure Purview data sources](https://docs.microsoft.com/azure/purview/purview-connector-overview#purview-data-sources),Use Azure Purview Azure Information Protection and Azure SQL Data Discovery and Classification to centrally scan classify and label sensitive data across Azure on-premises Microsoft 365 and other locations.,"Enhanced Implementation Context:
• Data classification overview: https://docs.microsoft.com/azure/cloud-adoption-framework/govern/policy-compliance/data-classification
• Azure Purview sensitivity labels: https://docs.microsoft.com/azure/purview/create-sensitivity-label
• Azure Information Protection: https://docs.microsoft.com/azure/information-protection/what-is-information-protection
• Azure SQL Data Discovery: https://docs.microsoft.com/azure/sql-database/sql-database-data-discovery-and-classification
• Azure Purview data sources: https://docs.microsoft.com/azure/purview/purview-connector-overview#purview-data-sources

Compliance Mappings:
• CIS Controls v8: 3.2, 3.7, 3.13
• NIST SP800-53 r4: RA-2, SC-28
• PCI-DSS v3.2.1: A3.2

Azure Policy Examples:
• Sensitive data in your SQL databases should be classified
• Implement data discovery and classification across all data stores
• Deploy Azure Purview for enterprise data governance
• Configure sensitivity labels for all data assets",ai_analysis,,Validated
hub-network.bicep,NS-1,Network Security,Establish network segmentation boundaries,CRITICAL,14.0,"The virtual network 'hub-vnet' defined at line 3 does not have any associated Network Security Group (NSG) on its subnet 'hub-subnet'. Without an NSG, there is no deny-by-default traffic control, enabling attackers to laterally move or gain initial access to resources deployed in this subnet. The blast radius includes all resources within the subnet, which could be exposed to unauthorized network traffic.","Associate a Network Security Group (NSG) with the 'hub-subnet' by adding the 'networkSecurityGroup' property to the subnet definition and referencing a properly configured NSG resource. Ensure the NSG enforces a deny-by-default policy and only allows required traffic. Reference: Azure Security Benchmark v3.0 NS-1.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/virtual-network/concepts-and-best-practices) | [Subnet management](https://docs.microsoft.com/azure/virtual-network/virtual-network-manage-subnet) | [NSG security rules](https://docs.microsoft.com/azure/virtual-network/tutorial-filter-network-traffic) | [Application security groups](https://docs.microsoft.com/azure/virtual-network/network-security-groups-overview#application-security-groups) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture)

🔵 Azure Guidance: Create virtual networks (VNets) as fundamental segmentation. Use network security groups (NSGs) and application security groups (ASGs) for traffic control. Implement deny-by-default approach.",[Enhanced Implementation Context](https://docs.microsoft.com/azure/virtual-network/concepts-and-best-practices) | [Subnet management](https://docs.microsoft.com/azure/virtual-network/virtual-network-manage-subnet) | [NSG security rules](https://docs.microsoft.com/azure/virtual-network/tutorial-filter-network-traffic) | [Application security groups](https://docs.microsoft.com/azure/virtual-network/network-security-groups-overview#application-security-groups) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture),Create virtual networks (VNets) as fundamental segmentation. Use network security groups (NSGs) and application security groups (ASGs) for traffic control. Implement deny-by-default approach.,"Enhanced Implementation Context:
• Virtual network concepts: https://docs.microsoft.com/azure/virtual-network/concepts-and-best-practices
• Subnet management: https://docs.microsoft.com/azure/virtual-network/virtual-network-manage-subnet
• NSG security rules: https://docs.microsoft.com/azure/virtual-network/tutorial-filter-network-traffic
• Application security groups: https://docs.microsoft.com/azure/virtual-network/network-security-groups-overview#application-security-groups
• Security architecture guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture

Compliance Mappings:
• CIS Controls v8: 3.12, 13.4, 4.4
• NIST SP800-53 r4: AC-4, SC-2, SC-7
• PCI-DSS v3.2.1: 1.1, 1.2, 1.3

Azure Policy Examples:
• Adaptive network hardening recommendations should be applied on internet facing virtual machines
• All network ports should be restricted on network security groups associated to your virtual machine
• Subnets should be associated with a Network Security Group",ai_analysis,,Validated
instance-config.bicep,IM-8,Identity Management,Restrict the exposure of credential and secrets,CRITICAL,46.0,"The variable 'monitoringGCSAuthId' at line 37 contains a value that appears to be a Key Vault DNS name ('airseccosinetest.geneva.keyvault.airaspcerts.cloudapp.net') directly embedded in the configuration. If this value is a secret identifier or credential reference and is not retrieved securely at runtime, it enables attackers with access to the configuration to enumerate or target the Key Vault, potentially leading to secret exposure or privilege escalation. The blast radius includes compromise of monitoring credentials and lateral movement to other Azure resources if the Key Vault is not properly secured.","Store all secret references and credentials in Azure Key Vault and retrieve them securely at runtime using managed identities. Remove any hardcoded Key Vault URLs or secret identifiers from configuration files. Implement Azure DevOps Credential Scanner or equivalent in CI/CD pipelines to detect and block embedded secrets. Reference: ASB IM-8.

📚 References: [Enhanced Implementation Context](https://secdevtools.azurewebsites.net/helpcredscan.html) | [GitHub secret scanning](https://docs.github.com/github/administering-a-repository/about-secret-scanning) | [Azure Key Vault developer guide](https://docs.microsoft.com/azure/key-vault/general/developers-guide) | [Secure coding practices](https://docs.microsoft.com/azure/security/develop/secure-dev-overview) | [Application security guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-application-security-devsecops)

🔵 Azure Guidance: Store secrets in Azure Key Vault instead of embedding in code. Implement Azure DevOps Credential Scanner or GitHub secret scanning. Use managed identities for Azure Key Vault access.",[Enhanced Implementation Context](https://secdevtools.azurewebsites.net/helpcredscan.html) | [GitHub secret scanning](https://docs.github.com/github/administering-a-repository/about-secret-scanning) | [Azure Key Vault developer guide](https://docs.microsoft.com/azure/key-vault/general/developers-guide) | [Secure coding practices](https://docs.microsoft.com/azure/security/develop/secure-dev-overview) | [Application security guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-application-security-devsecops),Store secrets in Azure Key Vault instead of embedding in code. Implement Azure DevOps Credential Scanner or GitHub secret scanning. Use managed identities for Azure Key Vault access.,"Enhanced Implementation Context:
• Azure DevOps Credential Scanner setup: https://secdevtools.azurewebsites.net/helpcredscan.html
• GitHub secret scanning: https://docs.github.com/github/administering-a-repository/about-secret-scanning
• Azure Key Vault developer guide: https://docs.microsoft.com/azure/key-vault/general/developers-guide
• Secure coding practices: https://docs.microsoft.com/azure/security/develop/secure-dev-overview
• Application security guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-application-security-devsecops

Compliance Mappings:
• CIS Controls v8: 16.9, 16.12
• NIST SP800-53 r4: IA-5
• PCI-DSS v3.2.1: 3.5, 6.3, 8.2

Azure Policy Examples:
• No applicable built-in policy (requires development process implementation)
• Implement credential scanning in CI/CD pipelines
• Enforce Azure Key Vault usage for secret storage
• Monitor and audit secret access patterns
• Require managed identities for Azure service authentication",ai_analysis,,Validated
instance-config.bicep,DP-1,Data Protection,Discover classify and label sensitive data,CRITICAL,67.0,"The output 'appConfigFields' at line 67 exposes the 'Admins' group by outputting the full list of administrator identities via '[${join(dynamicConfig.Admins, ', ')}]'. This creates an information disclosure risk, as attackers who gain access to deployment outputs or logs can enumerate privileged accounts, enabling targeted phishing, privilege escalation, or lateral movement. The blast radius includes potential compromise of all admin accounts and associated resources.","Remove or mask sensitive identity information from outputs. Do not output administrator or privileged user lists in deployment outputs. Instead, output only non-sensitive metadata or use Azure Purview/Azure Information Protection to classify and restrict access to sensitive outputs. Reference ASB control DP-1 for data classification and protection requirements.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/cloud-adoption-framework/govern/policy-compliance/data-classification) | [Azure Purview sensitivity labels](https://docs.microsoft.com/azure/purview/create-sensitivity-label) | [Azure Information Protection](https://docs.microsoft.com/azure/information-protection/what-is-information-protection) | [Azure SQL Data Discovery](https://docs.microsoft.com/azure/sql-database/sql-database-data-discovery-and-classification) | [Azure Purview data sources](https://docs.microsoft.com/azure/purview/purview-connector-overview#purview-data-sources)

🔵 Azure Guidance: Use Azure Purview Azure Information Protection and Azure SQL Data Discovery and Classification to centrally scan classify and label sensitive data across Azure on-premises Microsoft 365 and other loca...",[Enhanced Implementation Context](https://docs.microsoft.com/azure/cloud-adoption-framework/govern/policy-compliance/data-classification) | [Azure Purview sensitivity labels](https://docs.microsoft.com/azure/purview/create-sensitivity-label) | [Azure Information Protection](https://docs.microsoft.com/azure/information-protection/what-is-information-protection) | [Azure SQL Data Discovery](https://docs.microsoft.com/azure/sql-database/sql-database-data-discovery-and-classification) | [Azure Purview data sources](https://docs.microsoft.com/azure/purview/purview-connector-overview#purview-data-sources),Use Azure Purview Azure Information Protection and Azure SQL Data Discovery and Classification to centrally scan classify and label sensitive data across Azure on-premises Microsoft 365 and other locations.,"Enhanced Implementation Context:
• Data classification overview: https://docs.microsoft.com/azure/cloud-adoption-framework/govern/policy-compliance/data-classification
• Azure Purview sensitivity labels: https://docs.microsoft.com/azure/purview/create-sensitivity-label
• Azure Information Protection: https://docs.microsoft.com/azure/information-protection/what-is-information-protection
• Azure SQL Data Discovery: https://docs.microsoft.com/azure/sql-database/sql-database-data-discovery-and-classification
• Azure Purview data sources: https://docs.microsoft.com/azure/purview/purview-connector-overview#purview-data-sources

Compliance Mappings:
• CIS Controls v8: 3.2, 3.7, 3.13
• NIST SP800-53 r4: RA-2, SC-28
• PCI-DSS v3.2.1: A3.2

Azure Policy Examples:
• Sensitive data in your SQL databases should be classified
• Implement data discovery and classification across all data stores
• Deploy Azure Purview for enterprise data governance
• Configure sensitivity labels for all data assets",ai_analysis,,Validated
ip-rules.bicep,NS-1,Network Security,Establish network segmentation boundaries,CRITICAL,4.0,"The IP rule '*******/8' in 'corpNetIps' (line 4) allows an extremely broad public IP range, which can enable initial access, lateral movement, and data exfiltration from any host within the *******/8 block. This significantly increases the attack surface and blast radius, violating network segmentation and deny-by-default principles. Attackers with access to any IP in this range could potentially reach protected resources.","Restrict allowed IP ranges to only trusted, minimal, and specific addresses or subnets. Remove '*******/8' from 'corpNetIps' and replace with precise corporate IP ranges. Enforce deny-by-default on all network security groups and only allow required traffic from tightly controlled sources. Reference: ASB NS-1.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/virtual-network/concepts-and-best-practices) | [Subnet management](https://docs.microsoft.com/azure/virtual-network/virtual-network-manage-subnet) | [NSG security rules](https://docs.microsoft.com/azure/virtual-network/tutorial-filter-network-traffic) | [Application security groups](https://docs.microsoft.com/azure/virtual-network/network-security-groups-overview#application-security-groups) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture)

🔵 Azure Guidance: Create virtual networks (VNets) as fundamental segmentation. Use network security groups (NSGs) and application security groups (ASGs) for traffic control. Implement deny-by-default approach.",[Enhanced Implementation Context](https://docs.microsoft.com/azure/virtual-network/concepts-and-best-practices) | [Subnet management](https://docs.microsoft.com/azure/virtual-network/virtual-network-manage-subnet) | [NSG security rules](https://docs.microsoft.com/azure/virtual-network/tutorial-filter-network-traffic) | [Application security groups](https://docs.microsoft.com/azure/virtual-network/network-security-groups-overview#application-security-groups) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture),Create virtual networks (VNets) as fundamental segmentation. Use network security groups (NSGs) and application security groups (ASGs) for traffic control. Implement deny-by-default approach.,"Enhanced Implementation Context:
• Virtual network concepts: https://docs.microsoft.com/azure/virtual-network/concepts-and-best-practices
• Subnet management: https://docs.microsoft.com/azure/virtual-network/virtual-network-manage-subnet
• NSG security rules: https://docs.microsoft.com/azure/virtual-network/tutorial-filter-network-traffic
• Application security groups: https://docs.microsoft.com/azure/virtual-network/network-security-groups-overview#application-security-groups
• Security architecture guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture

Compliance Mappings:
• CIS Controls v8: 3.12, 13.4, 4.4
• NIST SP800-53 r4: AC-4, SC-2, SC-7
• PCI-DSS v3.2.1: 1.1, 1.2, 1.3

Azure Policy Examples:
• Adaptive network hardening recommendations should be applied on internet facing virtual machines
• All network ports should be restricted on network security groups associated to your virtual machine
• Subnets should be associated with a Network Security Group",ai_analysis,,Validated
ip-rules.bicep,NS-1,Network Security,Establish network segmentation boundaries,CRITICAL,5.0,"The IP rule '********/8' in 'corpNetIps' (line 5) allows a massive public IP range, exposing resources to a wide set of external addresses. This can enable unauthorized access, lateral movement, and data exfiltration, violating network segmentation and least privilege principles. Attackers from any IP in this range could attempt to access protected assets.","Limit allowed IPs to only those required for business operations. Remove '********/8' from 'corpNetIps' and use only specific, trusted subnets. Apply deny-by-default on NSGs and review all allow rules for necessity and scope. Reference: ASB NS-1.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/virtual-network/concepts-and-best-practices) | [Subnet management](https://docs.microsoft.com/azure/virtual-network/virtual-network-manage-subnet) | [NSG security rules](https://docs.microsoft.com/azure/virtual-network/tutorial-filter-network-traffic) | [Application security groups](https://docs.microsoft.com/azure/virtual-network/network-security-groups-overview#application-security-groups) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture)

🔵 Azure Guidance: Create virtual networks (VNets) as fundamental segmentation. Use network security groups (NSGs) and application security groups (ASGs) for traffic control. Implement deny-by-default approach.",[Enhanced Implementation Context](https://docs.microsoft.com/azure/virtual-network/concepts-and-best-practices) | [Subnet management](https://docs.microsoft.com/azure/virtual-network/virtual-network-manage-subnet) | [NSG security rules](https://docs.microsoft.com/azure/virtual-network/tutorial-filter-network-traffic) | [Application security groups](https://docs.microsoft.com/azure/virtual-network/network-security-groups-overview#application-security-groups) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture),Create virtual networks (VNets) as fundamental segmentation. Use network security groups (NSGs) and application security groups (ASGs) for traffic control. Implement deny-by-default approach.,"Enhanced Implementation Context:
• Virtual network concepts: https://docs.microsoft.com/azure/virtual-network/concepts-and-best-practices
• Subnet management: https://docs.microsoft.com/azure/virtual-network/virtual-network-manage-subnet
• NSG security rules: https://docs.microsoft.com/azure/virtual-network/tutorial-filter-network-traffic
• Application security groups: https://docs.microsoft.com/azure/virtual-network/network-security-groups-overview#application-security-groups
• Security architecture guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture

Compliance Mappings:
• CIS Controls v8: 3.12, 13.4, 4.4
• NIST SP800-53 r4: AC-4, SC-2, SC-7
• PCI-DSS v3.2.1: 1.1, 1.2, 1.3

Azure Policy Examples:
• Adaptive network hardening recommendations should be applied on internet facing virtual machines
• All network ports should be restricted on network security groups associated to your virtual machine
• Subnets should be associated with a Network Security Group",ai_analysis,,Validated
ip-rules.bicep,NS-1,Network Security,Establish network segmentation boundaries,CRITICAL,6.0,"The IP rule '20.0.0.0/8' in 'corpNetIps' (line 6) is overly permissive, granting access to an entire /8 public range. This increases the risk of initial access and lateral movement by unauthorized parties, expanding the blast radius in case of compromise.","Remove '20.0.0.0/8' from 'corpNetIps' and replace with only the smallest necessary subnets. Enforce deny-by-default and review all allow rules for business justification. Reference: ASB NS-1.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/virtual-network/concepts-and-best-practices) | [Subnet management](https://docs.microsoft.com/azure/virtual-network/virtual-network-manage-subnet) | [NSG security rules](https://docs.microsoft.com/azure/virtual-network/tutorial-filter-network-traffic) | [Application security groups](https://docs.microsoft.com/azure/virtual-network/network-security-groups-overview#application-security-groups) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture)

🔵 Azure Guidance: Create virtual networks (VNets) as fundamental segmentation. Use network security groups (NSGs) and application security groups (ASGs) for traffic control. Implement deny-by-default approach.",[Enhanced Implementation Context](https://docs.microsoft.com/azure/virtual-network/concepts-and-best-practices) | [Subnet management](https://docs.microsoft.com/azure/virtual-network/virtual-network-manage-subnet) | [NSG security rules](https://docs.microsoft.com/azure/virtual-network/tutorial-filter-network-traffic) | [Application security groups](https://docs.microsoft.com/azure/virtual-network/network-security-groups-overview#application-security-groups) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture),Create virtual networks (VNets) as fundamental segmentation. Use network security groups (NSGs) and application security groups (ASGs) for traffic control. Implement deny-by-default approach.,"Enhanced Implementation Context:
• Virtual network concepts: https://docs.microsoft.com/azure/virtual-network/concepts-and-best-practices
• Subnet management: https://docs.microsoft.com/azure/virtual-network/virtual-network-manage-subnet
• NSG security rules: https://docs.microsoft.com/azure/virtual-network/tutorial-filter-network-traffic
• Application security groups: https://docs.microsoft.com/azure/virtual-network/network-security-groups-overview#application-security-groups
• Security architecture guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture

Compliance Mappings:
• CIS Controls v8: 3.12, 13.4, 4.4
• NIST SP800-53 r4: AC-4, SC-2, SC-7
• PCI-DSS v3.2.1: 1.1, 1.2, 1.3

Azure Policy Examples:
• Adaptive network hardening recommendations should be applied on internet facing virtual machines
• All network ports should be restricted on network security groups associated to your virtual machine
• Subnets should be associated with a Network Security Group",ai_analysis,,Validated
ip-rules.bicep,NS-1,Network Security,Establish network segmentation boundaries,CRITICAL,7.0,"The IP rule '40.0.0.0/8' in 'corpNetIps' (line 7) allows an entire /8 public range, which is not aligned with network segmentation best practices. This can enable broad external access, increasing the risk of exploitation and data exposure.","Remove '40.0.0.0/8' from 'corpNetIps' and only allow specific, trusted IPs or subnets. Apply deny-by-default and restrict access to the minimum required. Reference: ASB NS-1.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/virtual-network/concepts-and-best-practices) | [Subnet management](https://docs.microsoft.com/azure/virtual-network/virtual-network-manage-subnet) | [NSG security rules](https://docs.microsoft.com/azure/virtual-network/tutorial-filter-network-traffic) | [Application security groups](https://docs.microsoft.com/azure/virtual-network/network-security-groups-overview#application-security-groups) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture)

🔵 Azure Guidance: Create virtual networks (VNets) as fundamental segmentation. Use network security groups (NSGs) and application security groups (ASGs) for traffic control. Implement deny-by-default approach.",[Enhanced Implementation Context](https://docs.microsoft.com/azure/virtual-network/concepts-and-best-practices) | [Subnet management](https://docs.microsoft.com/azure/virtual-network/virtual-network-manage-subnet) | [NSG security rules](https://docs.microsoft.com/azure/virtual-network/tutorial-filter-network-traffic) | [Application security groups](https://docs.microsoft.com/azure/virtual-network/network-security-groups-overview#application-security-groups) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture),Create virtual networks (VNets) as fundamental segmentation. Use network security groups (NSGs) and application security groups (ASGs) for traffic control. Implement deny-by-default approach.,"Enhanced Implementation Context:
• Virtual network concepts: https://docs.microsoft.com/azure/virtual-network/concepts-and-best-practices
• Subnet management: https://docs.microsoft.com/azure/virtual-network/virtual-network-manage-subnet
• NSG security rules: https://docs.microsoft.com/azure/virtual-network/tutorial-filter-network-traffic
• Application security groups: https://docs.microsoft.com/azure/virtual-network/network-security-groups-overview#application-security-groups
• Security architecture guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture

Compliance Mappings:
• CIS Controls v8: 3.12, 13.4, 4.4
• NIST SP800-53 r4: AC-4, SC-2, SC-7
• PCI-DSS v3.2.1: 1.1, 1.2, 1.3

Azure Policy Examples:
• Adaptive network hardening recommendations should be applied on internet facing virtual machines
• All network ports should be restricted on network security groups associated to your virtual machine
• Subnets should be associated with a Network Security Group",ai_analysis,,Validated
ip-rules.bicep,NS-1,Network Security,Establish network segmentation boundaries,CRITICAL,8.0,"The IP rule '********/8' in 'corpNetIps' (line 8) is excessively broad, exposing resources to a large public address space. This increases the risk of unauthorized access and network compromise.","Remove '********/8' from 'corpNetIps' and use only specific, trusted IPs. Enforce deny-by-default and review all allow rules for necessity. Reference: ASB NS-1.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/virtual-network/concepts-and-best-practices) | [Subnet management](https://docs.microsoft.com/azure/virtual-network/virtual-network-manage-subnet) | [NSG security rules](https://docs.microsoft.com/azure/virtual-network/tutorial-filter-network-traffic) | [Application security groups](https://docs.microsoft.com/azure/virtual-network/network-security-groups-overview#application-security-groups) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture)

🔵 Azure Guidance: Create virtual networks (VNets) as fundamental segmentation. Use network security groups (NSGs) and application security groups (ASGs) for traffic control. Implement deny-by-default approach.",[Enhanced Implementation Context](https://docs.microsoft.com/azure/virtual-network/concepts-and-best-practices) | [Subnet management](https://docs.microsoft.com/azure/virtual-network/virtual-network-manage-subnet) | [NSG security rules](https://docs.microsoft.com/azure/virtual-network/tutorial-filter-network-traffic) | [Application security groups](https://docs.microsoft.com/azure/virtual-network/network-security-groups-overview#application-security-groups) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture),Create virtual networks (VNets) as fundamental segmentation. Use network security groups (NSGs) and application security groups (ASGs) for traffic control. Implement deny-by-default approach.,"Enhanced Implementation Context:
• Virtual network concepts: https://docs.microsoft.com/azure/virtual-network/concepts-and-best-practices
• Subnet management: https://docs.microsoft.com/azure/virtual-network/virtual-network-manage-subnet
• NSG security rules: https://docs.microsoft.com/azure/virtual-network/tutorial-filter-network-traffic
• Application security groups: https://docs.microsoft.com/azure/virtual-network/network-security-groups-overview#application-security-groups
• Security architecture guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture

Compliance Mappings:
• CIS Controls v8: 3.12, 13.4, 4.4
• NIST SP800-53 r4: AC-4, SC-2, SC-7
• PCI-DSS v3.2.1: 1.1, 1.2, 1.3

Azure Policy Examples:
• Adaptive network hardening recommendations should be applied on internet facing virtual machines
• All network ports should be restricted on network security groups associated to your virtual machine
• Subnets should be associated with a Network Security Group",ai_analysis,,Validated
operational-insights.bicep,NS-2,Network Security,Secure cloud services with network controls,CRITICAL,72.0,"The 'Microsoft.OperationalInsights/workspaces' resource (insightsMonitorAccount) does not specify any network access restrictions or private endpoint configuration. By default, Log Analytics workspaces are accessible over the public internet, which enables attackers to attempt initial access, brute force, or data exfiltration attacks if credentials are compromised. The blast radius includes potential exposure of all logs and telemetry data stored in the workspace.","Restrict public network access to the Log Analytics workspace by setting 'publicNetworkAccess' to 'Disabled' and deploying a private endpoint. In Bicep, add the 'publicNetworkAccess' property with value 'Disabled' and configure a 'Microsoft.Network/privateEndpoints' resource targeting this workspace. Reference ASB control NS-2.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/private-link/private-link-overview) | [Private endpoints for Storage](https://docs.microsoft.com/azure/storage/common/storage-private-endpoints) | [SQL private endpoints](https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview) | [Key Vault private endpoints](https://docs.microsoft.com/azure/key-vault/general/private-link-service) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture)

🔵 Azure Guidance: Deploy private endpoints for Azure resources supporting Private Link. Disable public network access where feasible. Use VNet integration for services requiring private access.",[Enhanced Implementation Context](https://docs.microsoft.com/azure/private-link/private-link-overview) | [Private endpoints for Storage](https://docs.microsoft.com/azure/storage/common/storage-private-endpoints) | [SQL private endpoints](https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview) | [Key Vault private endpoints](https://docs.microsoft.com/azure/key-vault/general/private-link-service) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture),Deploy private endpoints for Azure resources supporting Private Link. Disable public network access where feasible. Use VNet integration for services requiring private access.,"Enhanced Implementation Context:
• Private Link overview: https://docs.microsoft.com/azure/private-link/private-link-overview
• Private endpoints for Storage: https://docs.microsoft.com/azure/storage/common/storage-private-endpoints
• SQL private endpoints: https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview
• Key Vault private endpoints: https://docs.microsoft.com/azure/key-vault/general/private-link-service
• Security architecture guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture

Compliance Mappings:
• CIS Controls v8: 3.12, 4.4
• NIST SP800-53 r4: AC-4, SC-2, SC-7
• PCI-DSS v3.2.1: 1.1, 1.2, 1.3

Azure Policy Examples:
• Private endpoint should be enabled for PostgreSQL/MySQL/MariaDB servers
• Storage accounts should use private link
• Azure SQL Database should disable public network access
• Cognitive Services accounts should restrict network access
• Container registries should use private link",ai_analysis,,Validated
operational-insights.bicep,DP-4,Data Protection,Enable data at rest encryption by default,HIGH,72.0,"The 'Microsoft.OperationalInsights/workspaces' resource does not explicitly configure encryption at rest settings. While Azure provides service-managed encryption by default, failure to specify customer-managed keys (CMK) or validate encryption settings may result in non-compliance with regulatory requirements and increases the risk of data exposure if Azure's default keys are compromised. Attackers gaining access to underlying storage could read or tamper with log data.","Explicitly configure encryption at rest using customer-managed keys (CMK) if required by your compliance policy. In Bicep, add the 'encryption' property with a reference to a Key Vault-managed key. Review and enforce encryption settings for all sensitive data stores. Reference ASB control DP-4.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/security/fundamentals/encryption-atrest#encryption-at-rest-in-microsoft-cloud-services) | [Data at rest double encryption](https://docs.microsoft.com/azure/security/fundamentals/encryption-models) | [Azure Disk Encryption](https://docs.microsoft.com/azure/virtual-machines/disk-encryption-overview) | [SQL Transparent Data Encryption](https://docs.microsoft.com/azure/azure-sql/database/transparent-data-encryption-tde-overview)

🔵 Azure Guidance: Many Azure services have data-at-rest encryption enabled by default using service-managed keys. Where not enabled by default enable encryption in Azure services or VMs for storage-level file-level or ...",[Enhanced Implementation Context](https://docs.microsoft.com/azure/security/fundamentals/encryption-atrest#encryption-at-rest-in-microsoft-cloud-services) | [Data at rest double encryption](https://docs.microsoft.com/azure/security/fundamentals/encryption-models) | [Azure Disk Encryption](https://docs.microsoft.com/azure/virtual-machines/disk-encryption-overview) | [SQL Transparent Data Encryption](https://docs.microsoft.com/azure/azure-sql/database/transparent-data-encryption-tde-overview),Many Azure services have data-at-rest encryption enabled by default using service-managed keys. Where not enabled by default enable encryption in Azure services or VMs for storage-level file-level or database-level encryption.,"Enhanced Implementation Context:
• Encryption at rest in Azure: https://docs.microsoft.com/azure/security/fundamentals/encryption-atrest#encryption-at-rest-in-microsoft-cloud-services
• Data at rest double encryption: https://docs.microsoft.com/azure/security/fundamentals/encryption-models
• Encryption model and key management: https://docs.microsoft.com/azure/security/fundamentals/encryption-models
• Azure Disk Encryption: https://docs.microsoft.com/azure/virtual-machines/disk-encryption-overview
• SQL Transparent Data Encryption: https://docs.microsoft.com/azure/azure-sql/database/transparent-data-encryption-tde-overview

Compliance Mappings:
• CIS Controls v8: 3.11
• NIST SP800-53 r4: SC-28
• PCI-DSS v3.2.1: 3.4, 3.5

Azure Policy Examples:
• Virtual machines should encrypt temp disks caches and data flows between Compute and Storage resources
• Transparent Data Encryption on SQL databases should be enabled
• Automation account variables should be encrypted
• Service Fabric clusters should have the ClusterProtectionLevel property set to EncryptAndSign
• Azure Cosmos DB accounts should use customer-managed keys to encrypt data at rest",ai_analysis,,Validated
operational-insights.bicep,DP-1,Data Protection,Discover classify and label sensitive data,CRITICAL,154.0,"The 'insightsComponents' resource at line 142 lacks explicit data classification and labeling for sensitive data. The 'tags' property at line 151 only includes a placeholder for GDPR ('Privacy.Asset.NonPersonal') and does not enforce or document data classification. This omission enables an attack vector where sensitive data may be ingested, stored, or processed in the Log Analytics workspace without proper discovery, classification, or labeling, increasing the risk of unauthorized access or data exfiltration. The blast radius includes potential exposure of unclassified sensitive data across all connected monitoring and analytics resources.","Implement Azure Purview or Azure Information Protection to scan, classify, and label all data ingested into the Log Analytics workspace. Update the 'tags' property to include actual data classification labels (e.g., 'Confidential', 'PII', 'Financial') and ensure automated classification policies are enforced. Reference: ASB DP-1.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/cloud-adoption-framework/govern/policy-compliance/data-classification) | [Azure Purview sensitivity labels](https://docs.microsoft.com/azure/purview/create-sensitivity-label) | [Azure Information Protection](https://docs.microsoft.com/azure/information-protection/what-is-information-protection) | [Azure SQL Data Discovery](https://docs.microsoft.com/azure/sql-database/sql-database-data-discovery-and-classification) | [Azure Purview data sources](https://docs.microsoft.com/azure/purview/purview-connector-overview#purview-data-sources)

🔵 Azure Guidance: Use Azure Purview Azure Information Protection and Azure SQL Data Discovery and Classification to centrally scan classify and label sensitive data across Azure on-premises Microsoft 365 and other loca...",[Enhanced Implementation Context](https://docs.microsoft.com/azure/cloud-adoption-framework/govern/policy-compliance/data-classification) | [Azure Purview sensitivity labels](https://docs.microsoft.com/azure/purview/create-sensitivity-label) | [Azure Information Protection](https://docs.microsoft.com/azure/information-protection/what-is-information-protection) | [Azure SQL Data Discovery](https://docs.microsoft.com/azure/sql-database/sql-database-data-discovery-and-classification) | [Azure Purview data sources](https://docs.microsoft.com/azure/purview/purview-connector-overview#purview-data-sources),Use Azure Purview Azure Information Protection and Azure SQL Data Discovery and Classification to centrally scan classify and label sensitive data across Azure on-premises Microsoft 365 and other locations.,"Enhanced Implementation Context:
• Data classification overview: https://docs.microsoft.com/azure/cloud-adoption-framework/govern/policy-compliance/data-classification
• Azure Purview sensitivity labels: https://docs.microsoft.com/azure/purview/create-sensitivity-label
• Azure Information Protection: https://docs.microsoft.com/azure/information-protection/what-is-information-protection
• Azure SQL Data Discovery: https://docs.microsoft.com/azure/sql-database/sql-database-data-discovery-and-classification
• Azure Purview data sources: https://docs.microsoft.com/azure/purview/purview-connector-overview#purview-data-sources

Compliance Mappings:
• CIS Controls v8: 3.2, 3.7, 3.13
• NIST SP800-53 r4: RA-2, SC-28
• PCI-DSS v3.2.1: A3.2

Azure Policy Examples:
• Sensitive data in your SQL databases should be classified
• Implement data discovery and classification across all data stores
• Deploy Azure Purview for enterprise data governance
• Configure sensitivity labels for all data assets",ai_analysis,,Validated
operational-insights.bicep,NS-2,Network Security,Secure cloud services with network controls,CRITICAL,166.0,"The 'insightsComponents' resource at line 142 does not restrict public network access or implement private endpoints for the Application Insights component. Without network restrictions, the resource may be accessible from the public internet, enabling initial access or data exfiltration by attackers. The blast radius includes all telemetry and monitoring data collected by Application Insights, which may contain sensitive application or infrastructure information.","Configure private endpoints for Application Insights and the associated Log Analytics workspace. Disable public network access by setting the appropriate networkAccessControl property or equivalent. Enforce VNet integration for all monitoring resources. Reference: ASB NS-2.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/private-link/private-link-overview) | [Private endpoints for Storage](https://docs.microsoft.com/azure/storage/common/storage-private-endpoints) | [SQL private endpoints](https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview) | [Key Vault private endpoints](https://docs.microsoft.com/azure/key-vault/general/private-link-service) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture)

🔵 Azure Guidance: Deploy private endpoints for Azure resources supporting Private Link. Disable public network access where feasible. Use VNet integration for services requiring private access.",[Enhanced Implementation Context](https://docs.microsoft.com/azure/private-link/private-link-overview) | [Private endpoints for Storage](https://docs.microsoft.com/azure/storage/common/storage-private-endpoints) | [SQL private endpoints](https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview) | [Key Vault private endpoints](https://docs.microsoft.com/azure/key-vault/general/private-link-service) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture),Deploy private endpoints for Azure resources supporting Private Link. Disable public network access where feasible. Use VNet integration for services requiring private access.,"Enhanced Implementation Context:
• Private Link overview: https://docs.microsoft.com/azure/private-link/private-link-overview
• Private endpoints for Storage: https://docs.microsoft.com/azure/storage/common/storage-private-endpoints
• SQL private endpoints: https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview
• Key Vault private endpoints: https://docs.microsoft.com/azure/key-vault/general/private-link-service
• Security architecture guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture

Compliance Mappings:
• CIS Controls v8: 3.12, 4.4
• NIST SP800-53 r4: AC-4, SC-2, SC-7
• PCI-DSS v3.2.1: 1.1, 1.2, 1.3

Azure Policy Examples:
• Private endpoint should be enabled for PostgreSQL/MySQL/MariaDB servers
• Storage accounts should use private link
• Azure SQL Database should disable public network access
• Cognitive Services accounts should restrict network access
• Container registries should use private link",ai_analysis,,Validated
operational-insights.bicep,DP-4,Data Protection,Enable data at rest encryption by default,HIGH,166.0,"The 'insightsComponents' resource at line 142 does not specify encryption settings for data at rest. Azure Monitor and Application Insights store telemetry and log data, which may include sensitive information. Without explicit configuration for encryption at rest, attackers gaining access to the underlying storage could read or modify data, increasing the blast radius to all monitored applications and infrastructure.","Ensure that encryption at rest is enabled for the Log Analytics workspace and Application Insights resources. Use service-managed keys by default, and consider customer-managed keys for higher assurance. Explicitly document and verify encryption settings in the resource configuration. Reference: ASB DP-4.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/security/fundamentals/encryption-atrest#encryption-at-rest-in-microsoft-cloud-services) | [Data at rest double encryption](https://docs.microsoft.com/azure/security/fundamentals/encryption-models) | [Azure Disk Encryption](https://docs.microsoft.com/azure/virtual-machines/disk-encryption-overview) | [SQL Transparent Data Encryption](https://docs.microsoft.com/azure/azure-sql/database/transparent-data-encryption-tde-overview)

🔵 Azure Guidance: Many Azure services have data-at-rest encryption enabled by default using service-managed keys. Where not enabled by default enable encryption in Azure services or VMs for storage-level file-level or ...",[Enhanced Implementation Context](https://docs.microsoft.com/azure/security/fundamentals/encryption-atrest#encryption-at-rest-in-microsoft-cloud-services) | [Data at rest double encryption](https://docs.microsoft.com/azure/security/fundamentals/encryption-models) | [Azure Disk Encryption](https://docs.microsoft.com/azure/virtual-machines/disk-encryption-overview) | [SQL Transparent Data Encryption](https://docs.microsoft.com/azure/azure-sql/database/transparent-data-encryption-tde-overview),Many Azure services have data-at-rest encryption enabled by default using service-managed keys. Where not enabled by default enable encryption in Azure services or VMs for storage-level file-level or database-level encryption.,"Enhanced Implementation Context:
• Encryption at rest in Azure: https://docs.microsoft.com/azure/security/fundamentals/encryption-atrest#encryption-at-rest-in-microsoft-cloud-services
• Data at rest double encryption: https://docs.microsoft.com/azure/security/fundamentals/encryption-models
• Encryption model and key management: https://docs.microsoft.com/azure/security/fundamentals/encryption-models
• Azure Disk Encryption: https://docs.microsoft.com/azure/virtual-machines/disk-encryption-overview
• SQL Transparent Data Encryption: https://docs.microsoft.com/azure/azure-sql/database/transparent-data-encryption-tde-overview

Compliance Mappings:
• CIS Controls v8: 3.11
• NIST SP800-53 r4: SC-28
• PCI-DSS v3.2.1: 3.4, 3.5

Azure Policy Examples:
• Virtual machines should encrypt temp disks caches and data flows between Compute and Storage resources
• Transparent Data Encryption on SQL databases should be enabled
• Automation account variables should be encrypted
• Service Fabric clusters should have the ClusterProtectionLevel property set to EncryptAndSign
• Azure Cosmos DB accounts should use customer-managed keys to encrypt data at rest",ai_analysis,,Validated
operational-insights.bicep,DP-1,Data Protection,Discover classify and label sensitive data,CRITICAL,173.0,"The output 'appInsightsInstrumentationKey' (line 173) exposes the Application Insights Instrumentation Key in plain text as a deployment output. This key is a sensitive credential that can be used by attackers to send arbitrary telemetry to your Application Insights resource, potentially leading to data poisoning, unauthorized data injection, or exfiltration of monitoring data. The blast radius includes unauthorized access to telemetry ingestion and possible lateral movement if the key is reused elsewhere.","Remove the 'appInsightsInstrumentationKey' output from the template or replace it with a reference to a secure location such as Azure Key Vault. Ensure that sensitive keys are never exposed in deployment outputs. Use Azure Purview or Azure Information Protection to classify and label sensitive outputs. Reference: DP-1.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/cloud-adoption-framework/govern/policy-compliance/data-classification) | [Azure Purview sensitivity labels](https://docs.microsoft.com/azure/purview/create-sensitivity-label) | [Azure Information Protection](https://docs.microsoft.com/azure/information-protection/what-is-information-protection) | [Azure SQL Data Discovery](https://docs.microsoft.com/azure/sql-database/sql-database-data-discovery-and-classification) | [Azure Purview data sources](https://docs.microsoft.com/azure/purview/purview-connector-overview#purview-data-sources)

🔵 Azure Guidance: Use Azure Purview Azure Information Protection and Azure SQL Data Discovery and Classification to centrally scan classify and label sensitive data across Azure on-premises Microsoft 365 and other loca...",[Enhanced Implementation Context](https://docs.microsoft.com/azure/cloud-adoption-framework/govern/policy-compliance/data-classification) | [Azure Purview sensitivity labels](https://docs.microsoft.com/azure/purview/create-sensitivity-label) | [Azure Information Protection](https://docs.microsoft.com/azure/information-protection/what-is-information-protection) | [Azure SQL Data Discovery](https://docs.microsoft.com/azure/sql-database/sql-database-data-discovery-and-classification) | [Azure Purview data sources](https://docs.microsoft.com/azure/purview/purview-connector-overview#purview-data-sources),Use Azure Purview Azure Information Protection and Azure SQL Data Discovery and Classification to centrally scan classify and label sensitive data across Azure on-premises Microsoft 365 and other locations.,"Enhanced Implementation Context:
• Data classification overview: https://docs.microsoft.com/azure/cloud-adoption-framework/govern/policy-compliance/data-classification
• Azure Purview sensitivity labels: https://docs.microsoft.com/azure/purview/create-sensitivity-label
• Azure Information Protection: https://docs.microsoft.com/azure/information-protection/what-is-information-protection
• Azure SQL Data Discovery: https://docs.microsoft.com/azure/sql-database/sql-database-data-discovery-and-classification
• Azure Purview data sources: https://docs.microsoft.com/azure/purview/purview-connector-overview#purview-data-sources

Compliance Mappings:
• CIS Controls v8: 3.2, 3.7, 3.13
• NIST SP800-53 r4: RA-2, SC-28
• PCI-DSS v3.2.1: A3.2

Azure Policy Examples:
• Sensitive data in your SQL databases should be classified
• Implement data discovery and classification across all data stores
• Deploy Azure Purview for enterprise data governance
• Configure sensitivity labels for all data assets",ai_analysis,,Validated
scaleset-networks.bicep,NS-1,Network Security,Establish network segmentation boundaries,CRITICAL,66.0,"The subnet configuration at line 66 sets 'defaultOutboundAccess: true', which enables default outbound internet access for the subnet. This exposes all resources in the subnet to the public internet unless explicitly restricted, creating an initial access vector for attackers and significantly increasing the blast radius in the event of a compromise. Without a Network Security Group (NSG) associated with the subnet, there is no deny-by-default control, violating network segmentation and exposure best practices.","Set 'defaultOutboundAccess' to false and associate a Network Security Group (NSG) with the subnet to enforce a deny-by-default policy. Explicitly define only required outbound rules. Example: Add an NSG resource and reference it in the subnet's 'networkSecurityGroup' property. Review and restrict outbound internet access to only necessary destinations.

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/virtual-network/concepts-and-best-practices) | [Subnet management](https://docs.microsoft.com/azure/virtual-network/virtual-network-manage-subnet) | [NSG security rules](https://docs.microsoft.com/azure/virtual-network/tutorial-filter-network-traffic) | [Application security groups](https://docs.microsoft.com/azure/virtual-network/network-security-groups-overview#application-security-groups) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture)

🔵 Azure Guidance: Create virtual networks (VNets) as fundamental segmentation. Use network security groups (NSGs) and application security groups (ASGs) for traffic control. Implement deny-by-default approach.",[Enhanced Implementation Context](https://docs.microsoft.com/azure/virtual-network/concepts-and-best-practices) | [Subnet management](https://docs.microsoft.com/azure/virtual-network/virtual-network-manage-subnet) | [NSG security rules](https://docs.microsoft.com/azure/virtual-network/tutorial-filter-network-traffic) | [Application security groups](https://docs.microsoft.com/azure/virtual-network/network-security-groups-overview#application-security-groups) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture),Create virtual networks (VNets) as fundamental segmentation. Use network security groups (NSGs) and application security groups (ASGs) for traffic control. Implement deny-by-default approach.,"Enhanced Implementation Context:
• Virtual network concepts: https://docs.microsoft.com/azure/virtual-network/concepts-and-best-practices
• Subnet management: https://docs.microsoft.com/azure/virtual-network/virtual-network-manage-subnet
• NSG security rules: https://docs.microsoft.com/azure/virtual-network/tutorial-filter-network-traffic
• Application security groups: https://docs.microsoft.com/azure/virtual-network/network-security-groups-overview#application-security-groups
• Security architecture guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture

Compliance Mappings:
• CIS Controls v8: 3.12, 13.4, 4.4
• NIST SP800-53 r4: AC-4, SC-2, SC-7
• PCI-DSS v3.2.1: 1.1, 1.2, 1.3

Azure Policy Examples:
• Adaptive network hardening recommendations should be applied on internet facing virtual machines
• All network ports should be restricted on network security groups associated to your virtual machine
• Subnets should be associated with a Network Security Group",ai_analysis,,Validated
server-farms.bicep,IM-8,Identity Management,Restrict the exposure of credential and secrets,CRITICAL,168.0,"The property 'settingValue' for 'CERTIFICATE_PASSWORD_GENEVACERT' is set to an empty string, indicating a missing or unset secret for a certificate password. This creates an attack vector for initial access or privilege escalation, as the Geneva certificate may be imported or used without a password, allowing unauthorized access to sensitive monitoring or diagnostic data. The blast radius includes potential compromise of all App Service resources using this certificate.","Set 'settingValue' to reference a secure secret stored in Azure Key Vault, and ensure the password is never hardcoded or left empty. Use managed identities for secure retrieval of secrets. Example: settingValue: '@Microsoft.KeyVault(SecretUri=<keyvault-secret-uri>)'. Review and rotate the certificate password regularly. Reference: ASB IM-8.

📚 References: [Enhanced Implementation Context](https://secdevtools.azurewebsites.net/helpcredscan.html) | [GitHub secret scanning](https://docs.github.com/github/administering-a-repository/about-secret-scanning) | [Azure Key Vault developer guide](https://docs.microsoft.com/azure/key-vault/general/developers-guide) | [Secure coding practices](https://docs.microsoft.com/azure/security/develop/secure-dev-overview) | [Application security guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-application-security-devsecops)

🔵 Azure Guidance: Store secrets in Azure Key Vault instead of embedding in code. Implement Azure DevOps Credential Scanner or GitHub secret scanning. Use managed identities for Azure Key Vault access.",[Enhanced Implementation Context](https://secdevtools.azurewebsites.net/helpcredscan.html) | [GitHub secret scanning](https://docs.github.com/github/administering-a-repository/about-secret-scanning) | [Azure Key Vault developer guide](https://docs.microsoft.com/azure/key-vault/general/developers-guide) | [Secure coding practices](https://docs.microsoft.com/azure/security/develop/secure-dev-overview) | [Application security guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-application-security-devsecops),Store secrets in Azure Key Vault instead of embedding in code. Implement Azure DevOps Credential Scanner or GitHub secret scanning. Use managed identities for Azure Key Vault access.,"Enhanced Implementation Context:
• Azure DevOps Credential Scanner setup: https://secdevtools.azurewebsites.net/helpcredscan.html
• GitHub secret scanning: https://docs.github.com/github/administering-a-repository/about-secret-scanning
• Azure Key Vault developer guide: https://docs.microsoft.com/azure/key-vault/general/developers-guide
• Secure coding practices: https://docs.microsoft.com/azure/security/develop/secure-dev-overview
• Application security guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-application-security-devsecops

Compliance Mappings:
• CIS Controls v8: 16.9, 16.12
• NIST SP800-53 r4: IA-5
• PCI-DSS v3.2.1: 3.5, 6.3, 8.2

Azure Policy Examples:
• No applicable built-in policy (requires development process implementation)
• Implement credential scanning in CI/CD pipelines
• Enforce Azure Key Vault usage for secret storage
• Monitor and audit secret access patterns
• Require managed identities for Azure service authentication",ai_analysis,,Validated
signalR.bicep,NS-2,Network Security,Secure cloud services with network controls,CRITICAL,4.0,"The SignalR resource 'Microsoft.SignalRService/signalR' defined at line 4 does not specify any network access controls such as private endpoints or restrictions on public network access. By default, Azure SignalR Service is accessible over the public internet, which enables initial access attack vectors (T1190: Exploit Public-Facing Application) and increases the blast radius for data exposure and lateral movement if credentials or tokens are compromised.","Explicitly configure the SignalR resource to use private endpoints by adding the 'networkAcls' property with 'publicNetworkAccess' set to 'Disabled' and defining private endpoint connections. This restricts access to trusted VNets only. Reference: https://docs.microsoft.com/azure/azure-signalr/signalr-private-endpoint. Example configuration:

properties: {
  ...
  networkAcls: {
    publicNetworkAccess: 'Disabled'
    ...
  }
  ...
}

This change is required to comply with ASB NS-2 (Secure cloud services by establishing private access points and disabling/restricting public network access when possible).

📚 References: [Enhanced Implementation Context](https://docs.microsoft.com/azure/private-link/private-link-overview) | [Private endpoints for Storage](https://docs.microsoft.com/azure/storage/common/storage-private-endpoints) | [SQL private endpoints](https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview) | [Key Vault private endpoints](https://docs.microsoft.com/azure/key-vault/general/private-link-service) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture)

🔵 Azure Guidance: Deploy private endpoints for Azure resources supporting Private Link. Disable public network access where feasible. Use VNet integration for services requiring private access.",[Enhanced Implementation Context](https://docs.microsoft.com/azure/private-link/private-link-overview) | [Private endpoints for Storage](https://docs.microsoft.com/azure/storage/common/storage-private-endpoints) | [SQL private endpoints](https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview) | [Key Vault private endpoints](https://docs.microsoft.com/azure/key-vault/general/private-link-service) | [Security architecture guidance](https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture),Deploy private endpoints for Azure resources supporting Private Link. Disable public network access where feasible. Use VNet integration for services requiring private access.,"Enhanced Implementation Context:
• Private Link overview: https://docs.microsoft.com/azure/private-link/private-link-overview
• Private endpoints for Storage: https://docs.microsoft.com/azure/storage/common/storage-private-endpoints
• SQL private endpoints: https://docs.microsoft.com/azure/azure-sql/database/private-endpoint-overview
• Key Vault private endpoints: https://docs.microsoft.com/azure/key-vault/general/private-link-service
• Security architecture guidance: https://docs.microsoft.com/azure/cloud-adoption-framework/organize/cloud-security-architecture

Compliance Mappings:
• CIS Controls v8: 3.12, 4.4
• NIST SP800-53 r4: AC-4, SC-2, SC-7
• PCI-DSS v3.2.1: 1.1, 1.2, 1.3

Azure Policy Examples:
• Private endpoint should be enabled for PostgreSQL/MySQL/MariaDB servers
• Storage accounts should use private link
• Azure SQL Database should disable public network access
• Cognitive Services accounts should restrict network access
• Container registries should use private link",ai_analysis,,Validated
,,,,,,,,,,,,,
=== ANALYSIS METADATA ===,,,,,,,,,,,,,
Total Findings: 55,,,,,,,,,,,,,
Validation Success Rate: 100.0%,,,,,,,,,,,,,
Corrections Made: 0,,,,,,,,,,,,,
Fictional IDs Prevented: 0,,,,,,,,,,,,,
Cross-Reference Findings: 5,,,,,,,,,,,,,
Analysis Timestamp: 2025-06-24T23:56:23.450011,,,,,,,,,,,,,
,,,,,,,,,,,,,
=== REFERENCE LINKS GUIDE ===,,,,,,,,,,,,,
The 'Reference Links' column contains URLs for implementation guidance,,,,,,,,,,,,,
The 'Azure Guidance' column contains Microsoft's specific recommendations,,,,,,,,,,,,,
The 'Implementation Context' column contains additional documentation links,,,,,,,,,,,,,
Links are formatted as: [Description](URL) for easy access,,,,,,,,,,,,,
