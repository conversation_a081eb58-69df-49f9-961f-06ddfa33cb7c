#!/usr/bin/env python
"""
Minimal Azure Security Benchmark PR Review Script

This script analyzes pull requests against the Azure Security Benchmark v3.0,
using Azure OpenAI in a RAG pattern to provide security recommendations.
"""

import os
import re
import sys
import json
import logging
import argparse
import requests
import tempfile
import pandas as pd
from pathlib import Path
from typing import Dict, List, Optional
from dotenv import load_dotenv, find_dotenv
import datetime
import base64
import urllib.parse
import openai
import time

# Azure DevOps SDK
from azure.devops.connection import Connection
from azure.devops.v6_0.git import GitPullRequestCommentThread, Comment
from msrest.authentication import BasicAuthentication

# OpenAI SDK - clean import with minimal dependencies
from openai import AzureOpenAI

# Optional import for Azure AD authentication
try:
    from azure.identity import DefaultAzureCredential, get_bearer_token_provider

    AZURE_IDENTITY_AVAILABLE = True
except ImportError:
    AZURE_IDENTITY_AVAILABLE = False

# Check if pandas is available
PANDAS_AVAILABLE = False
try:
    import pandas as pd
    import tempfile

    PANDAS_AVAILABLE = True
except ImportError:
    pass

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler(sys.stdout)],
)
logger = logging.getLogger(__name__)

# Mapping of severity levels to emojis
SEVERITY_EMOJIS = {"CRITICAL": "🔴", "HIGH": "🟠", "MEDIUM": "🟡", "LOW": "🔵"}

def get_openai_api_params(deployment_name: str, max_tokens: int = 3000, **kwargs) -> dict:
    """
    Get appropriate OpenAI API parameters based on model type.

    Args:
        deployment_name: The Azure OpenAI deployment name
        max_tokens: Maximum tokens to generate
        **kwargs: Additional parameters to pass to the API

    Returns:
        Dictionary with appropriate parameters for the model type
    """
    # Normalize deployment name for comparison
    model_name = deployment_name.lower()

    # O1 models (o1-preview, o1-mini) require max_completion_tokens
    # GPT-4o and other models can use either max_tokens or max_completion_tokens
    is_o1_model = any(pattern in model_name for pattern in [
        'o1-preview', 'o1-mini', 'o1_preview', 'o1_mini', 'o-1-preview', 'o-1-mini','o','o4-mini'
    ])

    # Base parameters
    params = {
        "model": deployment_name,
        **kwargs
    }

    if is_o1_model:
        # O1 models use max_completion_tokens and have restrictions
        params["max_completion_tokens"] = max_tokens
        # O1 models don't support temperature, seed, or response_format
        # Remove these if they were passed in kwargs
        params.pop("temperature", None)
        params.pop("seed", None)
        params.pop("response_format", None)
        logger.info(f"Using O1 model parameters for {deployment_name}")
    else:
        # GPT-4o and other models - use max_completion_tokens for consistency
        params["max_completion_tokens"] = max_tokens
        logger.info(f"Using standard model parameters for {deployment_name}")

    return params


class SecurityPRReviewer:
    """
    Reviews PR files for security issues based on Azure Security Benchmark v3.0.
    """

    # Comprehensive mapping of Azure resource types to their identifiers
    AZURE_RESOURCE_MAPPINGS = {
        "Storage": {
            "arm_types": [
                "Microsoft.Storage/storageAccounts",
                "Microsoft.Storage/storageAccounts/blobServices",
                "Microsoft.Storage/storageAccounts/blobServices/containers",
                "Microsoft.Storage/storageAccounts/fileServices",
                "Microsoft.Storage/storageAccounts/fileServices/shares",
                "Microsoft.Storage/storageAccounts/queueServices",
                "Microsoft.Storage/storageAccounts/queueServices/queues",
                "Microsoft.Storage/storageAccounts/tableServices",
                "Microsoft.Storage/storageAccounts/tableServices/tables",
                "Microsoft.Storage/storageAccounts/encryptionScopes",
                "Microsoft.Storage/storageAccounts/managementPolicies",
                "Microsoft.Storage/storageAccounts/objectReplicationPolicies",
                "Microsoft.Storage/storageAccounts/privateEndpointConnections",
            ],
            "terraform_types": [
                "azurerm_storage_account",
                "azurerm_storage_account_customer_managed_key",
                "azurerm_storage_account_network_rules",
                "azurerm_storage_container",
                "azurerm_storage_blob",
                "azurerm_storage_blob_inventory_policy",
                "azurerm_storage_share",
                "azurerm_storage_share_directory",
                "azurerm_storage_share_file",
                "azurerm_storage_queue",
                "azurerm_storage_table",
                "azurerm_storage_table_entity",
                "azurerm_storage_encryption_scope",
                "azurerm_storage_management_policy",
                "azurerm_storage_object_replication",
            ],
            "bicep_types": [
                "storageAccount",
                "storageAccounts",
                "blobServices",
                "blobContainer",
                "fileServices",
                "fileShare",
                "queueServices",
                "queue",
                "tableServices",
                "table",
                "managementPolicies",
                "encryptionScopes",
            ],
            "keywords": [
                "storage account",
                "blob",
                "container",
                "file share",
                "queue storage",
                "table storage",
                "storage encryption",
                "storage firewall",
                "storage network rules",
                "storage lifecycle",
                "immutable storage",
                "blob versioning",
                "soft delete",
                "storage tier",
                "storage replication",
            ],
        },
        "KeyVault": {
            "arm_types": [
                "Microsoft.KeyVault/vaults",
                "Microsoft.KeyVault/vaults/secrets",
                "Microsoft.KeyVault/vaults/keys",
                "Microsoft.KeyVault/vaults/certificates",
                "Microsoft.KeyVault/vaults/accessPolicies",
                "Microsoft.KeyVault/vaults/privateEndpointConnections",
                "Microsoft.KeyVault/managedHSMs",
                "Microsoft.KeyVault/managedHSMs/privateEndpointConnections",
            ],
            "terraform_types": [
                "azurerm_key_vault",
                "azurerm_key_vault_access_policy",
                "azurerm_key_vault_secret",
                "azurerm_key_vault_key",
                "azurerm_key_vault_certificate",
                "azurerm_key_vault_certificate_issuer",
                "azurerm_key_vault_managed_hardware_security_module",
                "azurerm_key_vault_managed_storage_account",
                "azurerm_key_vault_managed_storage_account_sas_token_definition",
            ],
            "bicep_types": [
                "keyVault",
                "vault",
                "vaults",
                "managedHSM",
                "managedHSMs",
                "secret",
                "key",
                "certificate",
                "accessPolicy",
                "accessPolicies",
            ],
            "keywords": [
                "key vault",
                "keyvault",
                "secrets",
                "certificates",
                "keys",
                "hsm",
                "hardware security module",
                "access policy",
                "rbac",
                "purge protection",
                "soft delete",
                "key rotation",
                "certificate authority",
                "cryptographic",
            ],
        },
        "SQL": {
            "arm_types": [
                "Microsoft.Sql/servers",
                "Microsoft.Sql/servers/databases",
                "Microsoft.Sql/servers/elasticPools",
                "Microsoft.Sql/servers/firewallRules",
                "Microsoft.Sql/servers/virtualNetworkRules",
                "Microsoft.Sql/servers/securityAlertPolicies",
                "Microsoft.Sql/servers/vulnerabilityAssessments",
                "Microsoft.Sql/servers/auditingSettings",
                "Microsoft.Sql/servers/devOpsAuditingSettings",
                "Microsoft.Sql/servers/encryptionProtector",
                "Microsoft.Sql/servers/keys",
                "Microsoft.Sql/servers/administrators",
                "Microsoft.Sql/servers/azureADOnlyAuthentications",
                "Microsoft.Sql/servers/connectionPolicies",
                "Microsoft.Sql/servers/databases/auditingSettings",
                "Microsoft.Sql/servers/databases/backupLongTermRetentionPolicies",
                "Microsoft.Sql/servers/databases/backupShortTermRetentionPolicies",
                "Microsoft.Sql/servers/databases/dataMaskingPolicies",
                "Microsoft.Sql/servers/databases/geoBackupPolicies",
                "Microsoft.Sql/servers/databases/securityAlertPolicies",
                "Microsoft.Sql/servers/databases/transparentDataEncryption",
                "Microsoft.Sql/servers/databases/vulnerabilityAssessments",
                "Microsoft.Sql/servers/databases/workloadGroups",
                "Microsoft.Sql/servers/privateEndpointConnections",
                "Microsoft.Sql/managedInstances",
                "Microsoft.Sql/managedInstances/databases",
                "Microsoft.Sql/managedInstances/securityAlertPolicies",
                "Microsoft.Sql/managedInstances/vulnerabilityAssessments",
                "Microsoft.Sql/managedInstances/administrators",
                "Microsoft.Sql/managedInstances/azureADOnlyAuthentications",
                "Microsoft.Sql/managedInstances/encryptionProtector",
                "Microsoft.Sql/managedInstances/keys",
                "Microsoft.Sql/managedInstances/privateEndpointConnections",
            ],
            "terraform_types": [
                "azurerm_mssql_server",
                "azurerm_sql_server",
                "azurerm_mssql_database",
                "azurerm_sql_database",
                "azurerm_mssql_database_extended_auditing_policy",
                "azurerm_mssql_elasticpool",
                "azurerm_sql_elasticpool",
                "azurerm_mssql_managed_instance",
                "azurerm_mssql_managed_database",
                "azurerm_sql_firewall_rule",
                "azurerm_mssql_firewall_rule",
                "azurerm_sql_virtual_network_rule",
                "azurerm_mssql_virtual_network_rule",
                "azurerm_mssql_server_security_alert_policy",
                "azurerm_mssql_database_vulnerability_assessment_rule_baseline",
                "azurerm_mssql_server_vulnerability_assessment",
                "azurerm_mssql_server_extended_auditing_policy",
                "azurerm_sql_active_directory_administrator",
                "azurerm_mssql_server_microsoft_support_auditing_policy",
                "azurerm_mssql_server_transparent_data_encryption",
                "azurerm_mssql_database_transparent_data_encryption",
                "azurerm_mssql_outbound_firewall_rule",
                "azurerm_mssql_managed_instance_security_alert_policy",
                "azurerm_mssql_managed_instance_vulnerability_assessment",
                "azurerm_mssql_managed_instance_transparent_data_encryption",
                "azurerm_mssql_managed_instance_active_directory_administrator",
                "azurerm_mssql_job_agent",
                "azurerm_mssql_job_credential",
                "azurerm_mssql_elastic_job_agent",
            ],
            "bicep_types": [
                "sqlServer",
                "sqlDatabase",
                "sql",
                "server",
                "database",
                "managedInstance",
                "managedDatabase",
                "elasticPool",
                "firewallRule",
                "virtualNetworkRule",
                "securityAlertPolicy",
                "vulnerabilityAssessment",
                "auditingSettings",
                "encryptionProtector",
                "transparentDataEncryption",
                "administrator",
                "azureADOnlyAuthentication",
                "connectionPolicy",
                "dataMaskingPolicy",
                "backupLongTermRetentionPolicy",
            ],
            "keywords": [
                "sql server",
                "sql database",
                "managed instance",
                "elastic pool",
                "mssql",
                "azure sql",
                "database server",
                "sql managed instance",
                "sql firewall",
                "sql security",
                "sql audit",
                "sql encryption",
                "sql tde",
                "transparent data encryption",
                "sql vulnerability",
                "sql assessment",
                "sql authentication",
                "azure ad authentication",
                "sql admin",
                "sql connection",
                "data masking",
                "always encrypted",
                "row level security",
                "dynamic data masking",
                "geo replication",
                "failover group",
                "backup retention",
            ],
        },
        "Network": {
            "arm_types": [
                "Microsoft.Network/virtualNetworks",
                "Microsoft.Network/virtualNetworks/subnets",
                "Microsoft.Network/virtualNetworks/virtualNetworkPeerings",
                "Microsoft.Network/networkSecurityGroups",
                "Microsoft.Network/networkSecurityGroups/securityRules",
                "Microsoft.Network/applicationGateways",
                "Microsoft.Network/azureFirewalls",
                "Microsoft.Network/azureFirewallPolicies",
                "Microsoft.Network/firewallPolicies",
                "Microsoft.Network/firewallPolicies/ruleCollectionGroups",
                "Microsoft.Network/bastionHosts",
                "Microsoft.Network/ddosProtectionPlans",
                "Microsoft.Network/expressRouteCircuits",
                "Microsoft.Network/frontDoors",
                "Microsoft.Network/frontDoorWebApplicationFirewallPolicies",
                "Microsoft.Network/loadBalancers",
                "Microsoft.Network/localNetworkGateways",
                "Microsoft.Network/natGateways",
                "Microsoft.Network/networkInterfaces",
                "Microsoft.Network/networkWatchers",
                "Microsoft.Network/privateEndpoints",
                "Microsoft.Network/privateLinkServices",
                "Microsoft.Network/publicIPAddresses",
                "Microsoft.Network/publicIPPrefixes",
                "Microsoft.Network/routeTables",
                "Microsoft.Network/serviceEndpointPolicies",
                "Microsoft.Network/trafficManagerProfiles",
                "Microsoft.Network/virtualHubs",
                "Microsoft.Network/virtualNetworkGateways",
                "Microsoft.Network/virtualWans",
                "Microsoft.Network/vpnGateways",
                "Microsoft.Network/vpnSites",
                "Microsoft.Network/applicationSecurityGroups",
                "Microsoft.Network/ipGroups",
            ],
            "terraform_types": [
                "azurerm_virtual_network",
                "azurerm_subnet",
                "azurerm_subnet_network_security_group_association",
                "azurerm_subnet_route_table_association",
                "azurerm_virtual_network_peering",
                "azurerm_network_security_group",
                "azurerm_network_security_rule",
                "azurerm_application_gateway",
                "azurerm_firewall",
                "azurerm_firewall_policy",
                "azurerm_firewall_policy_rule_collection_group",
                "azurerm_bastion_host",
                "azurerm_network_ddos_protection_plan",
                "azurerm_express_route_circuit",
                "azurerm_frontdoor",
                "azurerm_frontdoor_firewall_policy",
                "azurerm_lb",
                "azurerm_lb_backend_address_pool",
                "azurerm_lb_nat_rule",
                "azurerm_lb_probe",
                "azurerm_lb_rule",
                "azurerm_local_network_gateway",
                "azurerm_nat_gateway",
                "azurerm_network_interface",
                "azurerm_network_interface_application_gateway_backend_address_pool_association",
                "azurerm_network_interface_backend_address_pool_association",
                "azurerm_network_interface_nat_rule_association",
                "azurerm_network_interface_security_group_association",
                "azurerm_network_watcher",
                "azurerm_network_watcher_flow_log",
                "azurerm_private_endpoint",
                "azurerm_private_link_service",
                "azurerm_public_ip",
                "azurerm_public_ip_prefix",
                "azurerm_route",
                "azurerm_route_table",
                "azurerm_traffic_manager_profile",
                "azurerm_virtual_hub",
                "azurerm_virtual_network_gateway",
                "azurerm_virtual_network_gateway_connection",
                "azurerm_virtual_wan",
                "azurerm_vpn_gateway",
                "azurerm_vpn_site",
                "azurerm_application_security_group",
                "azurerm_ip_group",
                "azurerm_web_application_firewall_policy",
            ],
            "bicep_types": [
                "virtualNetwork",
                "vnet",
                "subnet",
                "networkSecurityGroup",
                "nsg",
                "securityRule",
                "applicationGateway",
                "appGateway",
                "firewall",
                "azureFirewall",
                "firewallPolicy",
                "bastionHost",
                "bastion",
                "ddosProtectionPlan",
                "expressRouteCircuit",
                "frontDoor",
                "loadBalancer",
                "lb",
                "localNetworkGateway",
                "natGateway",
                "networkInterface",
                "nic",
                "networkWatcher",
                "privateEndpoint",
                "privateLinkService",
                "publicIPAddress",
                "publicIP",
                "pip",
                "routeTable",
                "trafficManagerProfile",
                "virtualHub",
                "virtualNetworkGateway",
                "vpnGateway",
                "virtualWan",
                "applicationSecurityGroup",
                "asg",
                "ipGroup",
                "wafPolicy",
            ],
            "keywords": [
                "virtual network",
                "vnet",
                "subnet",
                "nsg",
                "network security group",
                "firewall",
                "azure firewall",
                "application gateway",
                "app gateway",
                "waf",
                "web application firewall",
                "load balancer",
                "traffic manager",
                "front door",
                "bastion",
                "ddos protection",
                "express route",
                "vpn",
                "vpn gateway",
                "virtual network gateway",
                "peering",
                "private endpoint",
                "private link",
                "service endpoint",
                "network interface",
                "nic",
                "public ip",
                "nat gateway",
                "route table",
                "udr",
                "user defined route",
                "network watcher",
                "flow logs",
                "packet capture",
                "network security",
                "network isolation",
                "network segmentation",
                "hub and spoke",
                "virtual wan",
                "sd-wan",
            ],
        },
        "Compute": {
            "arm_types": [
                "Microsoft.Compute/virtualMachines",
                "Microsoft.Compute/virtualMachines/extensions",
                "Microsoft.Compute/virtualMachineScaleSets",
                "Microsoft.Compute/virtualMachineScaleSets/extensions",
                "Microsoft.Compute/virtualMachineScaleSets/virtualMachines",
                "Microsoft.Compute/availabilitySets",
                "Microsoft.Compute/proximityPlacementGroups",
                "Microsoft.Compute/dedicatedHosts",
                "Microsoft.Compute/dedicatedHostGroups",
                "Microsoft.Compute/disks",
                "Microsoft.Compute/diskEncryptionSets",
                "Microsoft.Compute/diskAccesses",
                "Microsoft.Compute/snapshots",
                "Microsoft.Compute/images",
                "Microsoft.Compute/galleries",
                "Microsoft.Compute/galleries/images",
                "Microsoft.Compute/galleries/images/versions",
                "Microsoft.Compute/restorePointCollections",
                "Microsoft.Compute/sshPublicKeys",
                "Microsoft.Compute/capacityReservationGroups",
                "Microsoft.Compute/capacityReservationGroups/capacityReservations",
            ],
            "terraform_types": [
                "azurerm_virtual_machine",
                "azurerm_linux_virtual_machine",
                "azurerm_windows_virtual_machine",
                "azurerm_virtual_machine_extension",
                "azurerm_virtual_machine_scale_set",
                "azurerm_linux_virtual_machine_scale_set",
                "azurerm_windows_virtual_machine_scale_set",
                "azurerm_virtual_machine_scale_set_extension",
                "azurerm_orchestrated_virtual_machine_scale_set",
                "azurerm_availability_set",
                "azurerm_proximity_placement_group",
                "azurerm_dedicated_host",
                "azurerm_dedicated_host_group",
                "azurerm_managed_disk",
                "azurerm_disk_encryption_set",
                "azurerm_disk_access",
                "azurerm_snapshot",
                "azurerm_image",
                "azurerm_shared_image_gallery",
                "azurerm_shared_image",
                "azurerm_shared_image_version",
                "azurerm_virtual_machine_restore_point_collection",
                "azurerm_ssh_public_key",
                "azurerm_capacity_reservation",
                "azurerm_capacity_reservation_group",
                "azurerm_virtual_machine_data_disk_attachment",
                "azurerm_virtual_machine_scale_set_packet_capture",
            ],
            "bicep_types": [
                "virtualMachine",
                "vm",
                "virtualMachineScaleSet",
                "vmss",
                "virtualMachineExtension",
                "vmExtension",
                "availabilitySet",
                "proximityPlacementGroup",
                "dedicatedHost",
                "dedicatedHostGroup",
                "disk",
                "managedDisk",
                "diskEncryptionSet",
                "diskAccess",
                "snapshot",
                "image",
                "gallery",
                "galleryImage",
                "galleryImageVersion",
                "restorePointCollection",
                "sshPublicKey",
                "capacityReservation",
                "capacityReservationGroup",
                "computeResource",
            ],
            "keywords": [
                "virtual machine",
                "vm",
                "vmss",
                "scale set",
                "virtual machine scale set",
                "compute",
                "ubuntu",
                "windows server",
                "linux",
                "redhat",
                "centos",
                "debian",
                "suse",
                "trusted launch",
                "secure boot",
                "vtpm",
                "guest attestation",
                "managed disk",
                "os disk",
                "data disk",
                "ssh key",
                "rdp",
                "password authentication",
                "key authentication",
                "network interface",
                "diagnostic settings",
                "extension",
                "custom script extension",
                "vm extension",
                "availability set",
                "availability zone",
                "proximity placement",
                "dedicated host",
                "spot instance",
                "reserved instance",
                "disk encryption",
                "azure disk encryption",
                "encryption at host",
                "accelerated networking",
                "managed identity",
                "system assigned identity",
                "user assigned identity",
                "boot diagnostics",
                "serial console",
                "vm backup",
                "disaster recovery",
                "site recovery",
                "update management",
                "patch management",
                "auto shutdown",
                "vm insights",
            ],
        },
        "AppService": {
            "arm_types": [
                "Microsoft.Web/sites",
                "Microsoft.Web/sites/config",
                "Microsoft.Web/sites/slots",
                "Microsoft.Web/sites/slots/config",
                "Microsoft.Web/serverFarms",
                "Microsoft.Web/certificates",
                "Microsoft.Web/connectionGateways",
                "Microsoft.Web/connections",
                "Microsoft.Web/customApis",
                "Microsoft.Web/deletedSites",
                "Microsoft.Web/deploymentLocations",
                "Microsoft.Web/georegions",
                "Microsoft.Web/hostingEnvironments",
                "Microsoft.Web/kubeEnvironments",
                "Microsoft.Web/publishingUsers",
                "Microsoft.Web/recommendations",
                "Microsoft.Web/resourceHealthMetadata",
                "Microsoft.Web/runtimes",
                "Microsoft.Web/serverFarms/eventGridFilters",
                "Microsoft.Web/serverFarms/firstPartyApps",
                "Microsoft.Web/serverFarms/firstPartyApps/keyVaultSettings",
                "Microsoft.Web/sites/deployments",
                "Microsoft.Web/sites/domainOwnershipIdentifiers",
                "Microsoft.Web/sites/extensions",
                "Microsoft.Web/sites/functions",
                "Microsoft.Web/sites/hostNameBindings",
                "Microsoft.Web/sites/hybridconnection",
                "Microsoft.Web/sites/instances",
                "Microsoft.Web/sites/instances/extensions",
                "Microsoft.Web/sites/premieraddons",
                "Microsoft.Web/sites/privateEndpointConnections",
                "Microsoft.Web/sites/processes",
                "Microsoft.Web/sites/publicCertificates",
                "Microsoft.Web/sites/recommendationHistory",
                "Microsoft.Web/sites/resourceHealthMetadata",
                "Microsoft.Web/sites/slots/deployments",
                "Microsoft.Web/sites/slots/domainOwnershipIdentifiers",
                "Microsoft.Web/sites/slots/extensions",
                "Microsoft.Web/sites/slots/functions",
                "Microsoft.Web/sites/slots/hostNameBindings",
                "Microsoft.Web/sites/slots/hybridconnection",
                "Microsoft.Web/sites/slots/instances",
                "Microsoft.Web/sites/slots/instances/extensions",
                "Microsoft.Web/sites/slots/premieraddons",
                "Microsoft.Web/sites/slots/privateEndpointConnections",
                "Microsoft.Web/sites/slots/processes",
                "Microsoft.Web/sites/slots/publicCertificates",
                "Microsoft.Web/sites/slots/virtualNetworkConnections",
                "Microsoft.Web/sites/virtualNetworkConnections",
                "Microsoft.Web/sourceControls",
                "Microsoft.Web/staticSites",
                "Microsoft.Web/staticSites/builds",
                "Microsoft.Web/staticSites/config",
                "Microsoft.Web/staticSites/customDomains",
                "Microsoft.Web/staticSites/privateEndpointConnections",
                "Microsoft.Web/validate",
                "Microsoft.Web/verifyHostingEnvironmentVnet",
            ],
            "terraform_types": [
                "azurerm_app_service",
                "azurerm_app_service_plan",
                "azurerm_app_service_slot",
                "azurerm_app_service_active_slot",
                "azurerm_app_service_certificate",
                "azurerm_app_service_certificate_binding",
                "azurerm_app_service_certificate_order",
                "azurerm_app_service_custom_hostname_binding",
                "azurerm_app_service_hybrid_connection",
                "azurerm_app_service_managed_certificate",
                "azurerm_app_service_source_control_token",
                "azurerm_app_service_virtual_network_swift_connection",
                "azurerm_function_app",
                "azurerm_function_app_slot",
                "azurerm_function_app_active_slot",
                "azurerm_function_app_hybrid_connection",
                "azurerm_linux_function_app",
                "azurerm_linux_function_app_slot",
                "azurerm_linux_web_app",
                "azurerm_linux_web_app_slot",
                "azurerm_logic_app_action_custom",
                "azurerm_logic_app_action_http",
                "azurerm_logic_app_integration_account",
                "azurerm_logic_app_trigger_custom",
                "azurerm_logic_app_trigger_http_request",
                "azurerm_logic_app_trigger_recurrence",
                "azurerm_logic_app_workflow",
                "azurerm_service_plan",
                "azurerm_source_control_token",
                "azurerm_static_site",
                "azurerm_static_site_custom_domain",
                "azurerm_web_application_firewall_policy",
                "azurerm_windows_function_app",
                "azurerm_windows_function_app_slot",
                "azurerm_windows_web_app",
                "azurerm_windows_web_app_slot",
            ],
            "bicep_types": [
                "site",
                "sites",
                "webApp",
                "functionApp",
                "appService",
                "appServicePlan",
                "serverfarm",
                "serverFarms",
                "webAppSlot",
                "functionAppSlot",
                "slot",
                "certificate",
                "customDomain",
                "hostNameBinding",
                "hybridConnection",
                "virtualNetworkConnection",
                "staticSite",
                "logicApp",
                "workflow",
                "apiConnection",
                "customApi",
            ],
            "keywords": [
                "app service",
                "web app",
                "function app",
                "api app",
                "mobile app",
                "app service plan",
                "service plan",
                "consumption plan",
                "premium plan",
                "dedicated plan",
                "isolated plan",
                "app service environment",
                "ase",
                "deployment slot",
                "staging slot",
                "production slot",
                "custom domain",
                "ssl certificate",
                "tls certificate",
                "managed certificate",
                "hybrid connection",
                "vnet integration",
                "private endpoint",
                "access restriction",
                "ip restriction",
                "cors",
                "authentication",
                "authorization",
                "easy auth",
                "managed identity",
                "deployment center",
                "continuous deployment",
                "ci/cd",
                "github actions",
                "azure devops",
                "kudu",
                "scm",
                "diagnostic logs",
                "application insights",
                "auto scale",
                "scale out",
                "scale up",
                "always on",
                "health check",
                "backup",
                "disaster recovery",
                "traffic manager",
                "front door",
                "cdn",
                "static web app",
                "serverless",
                "functions",
                "durable functions",
                "logic apps",
                "api management",
            ],
        },
        "Container": {
            "arm_types": [
                "Microsoft.ContainerService/managedClusters",
                "Microsoft.ContainerService/managedClusters/agentPools",
                "Microsoft.ContainerService/managedClusters/maintenanceConfigurations",
                "Microsoft.ContainerService/managedClusters/privateEndpointConnections",
                "Microsoft.ContainerRegistry/registries",
                "Microsoft.ContainerRegistry/registries/agentPools",
                "Microsoft.ContainerRegistry/registries/buildTasks",
                "Microsoft.ContainerRegistry/registries/buildTasks/steps",
                "Microsoft.ContainerRegistry/registries/builds",
                "Microsoft.ContainerRegistry/registries/connectedRegistries",
                "Microsoft.ContainerRegistry/registries/exportPipelines",
                "Microsoft.ContainerRegistry/registries/importPipelines",
                "Microsoft.ContainerRegistry/registries/pipelineRuns",
                "Microsoft.ContainerRegistry/registries/privateEndpointConnections",
                "Microsoft.ContainerRegistry/registries/replications",
                "Microsoft.ContainerRegistry/registries/runs",
                "Microsoft.ContainerRegistry/registries/scopeMaps",
                "Microsoft.ContainerRegistry/registries/taskRuns",
                "Microsoft.ContainerRegistry/registries/tasks",
                "Microsoft.ContainerRegistry/registries/tokens",
                "Microsoft.ContainerRegistry/registries/webhooks",
                "Microsoft.ContainerInstance/containerGroups",
                "Microsoft.ContainerInstance/serviceAssociationLinks",
            ],
            "terraform_types": [
                "azurerm_kubernetes_cluster",
                "azurerm_kubernetes_cluster_node_pool",
                "azurerm_kubernetes_cluster_extension",
                "azurerm_kubernetes_fleet_manager",
                "azurerm_container_registry",
                "azurerm_container_registry_agent_pool",
                "azurerm_container_registry_scope_map",
                "azurerm_container_registry_task",
                "azurerm_container_registry_task_schedule_run_now",
                "azurerm_container_registry_token",
                "azurerm_container_registry_token_password",
                "azurerm_container_registry_webhook",
                "azurerm_container_connected_registry",
                "azurerm_container_group",
                "azurerm_container_app",
                "azurerm_container_app_environment",
                "azurerm_container_app_environment_certificate",
                "azurerm_container_app_environment_custom_domain",
                "azurerm_container_app_environment_dapr_component",
                "azurerm_container_app_environment_storage",
            ],
            "bicep_types": [
                "managedCluster",
                "aks",
                "aksCluster",
                "kubernetesCluster",
                "agentPool",
                "nodePool",
                "containerRegistry",
                "acr",
                "registry",
                "containerGroup",
                "containerInstance",
                "aci",
                "containerApp",
                "containerAppEnvironment",
                "scopeMap",
                "token",
                "webhook",
                "replication",
                "task",
            ],
            "keywords": [
                "aks",
                "kubernetes",
                "k8s",
                "container",
                "docker",
                "containerization",
                "orchestration",
                "cluster",
                "node pool",
                "agent pool",
                "kubelet",
                "kubectl",
                "helm",
                "container registry",
                "acr",
                "docker registry",
                "image",
                "container image",
                "dockerfile",
                "container instance",
                "aci",
                "container group",
                "container apps",
                "microservices",
                "service mesh",
                "istio",
                "linkerd",
                "ingress",
                "ingress controller",
                "load balancer",
                "cluster autoscaler",
                "horizontal pod autoscaler",
                "hpa",
                "vertical pod autoscaler",
                "vpa",
                "rbac",
                "role based access control",
                "service principal",
                "managed identity",
                "aad integration",
                "azure active directory",
                "network policy",
                "calico",
                "azure cni",
                "kubenet",
                "private cluster",
                "api server",
                "authorized ip",
                "pod security",
                "admission controller",
                "azure policy",
                "gatekeeper",
                "opa",
                "open policy agent",
                "container insights",
                "prometheus",
                "grafana",
                "monitoring",
                "logging",
                "fluentd",
                "fluent bit",
                "azure monitor",
                "log analytics",
                "container security",
                "image scanning",
                "vulnerability scanning",
                "azure defender",
                "devsecops",
                "gitops",
                "flux",
                "argo cd",
                "ci/cd",
                "azure devops",
                "github actions",
                "jenkins",
                "registry replication",
                "geo replication",
                "webhook",
                "acr tasks",
                "base image update",
            ],
        },
        "CosmosDB": {
            "arm_types": [
                "Microsoft.DocumentDB/databaseAccounts",
                "Microsoft.DocumentDB/databaseAccounts/apis/databases",
                "Microsoft.DocumentDB/databaseAccounts/apis/databases/collections",
                "Microsoft.DocumentDB/databaseAccounts/apis/databases/containers",
                "Microsoft.DocumentDB/databaseAccounts/apis/databases/graphs",
                "Microsoft.DocumentDB/databaseAccounts/apis/keyspaces",
                "Microsoft.DocumentDB/databaseAccounts/apis/keyspaces/tables",
                "Microsoft.DocumentDB/databaseAccounts/apis/tables",
                "Microsoft.DocumentDB/databaseAccounts/cassandraKeyspaces",
                "Microsoft.DocumentDB/databaseAccounts/cassandraKeyspaces/tables",
                "Microsoft.DocumentDB/databaseAccounts/gremlinDatabases",
                "Microsoft.DocumentDB/databaseAccounts/gremlinDatabases/graphs",
                "Microsoft.DocumentDB/databaseAccounts/mongodbDatabases",
                "Microsoft.DocumentDB/databaseAccounts/mongodbDatabases/collections",
                "Microsoft.DocumentDB/databaseAccounts/notebookWorkspaces",
                "Microsoft.DocumentDB/databaseAccounts/privateEndpointConnections",
                "Microsoft.DocumentDB/databaseAccounts/privateLinkResources",
                "Microsoft.DocumentDB/databaseAccounts/sqlDatabases",
                "Microsoft.DocumentDB/databaseAccounts/sqlDatabases/containers",
                "Microsoft.DocumentDB/databaseAccounts/sqlDatabases/containers/storedProcedures",
                "Microsoft.DocumentDB/databaseAccounts/sqlDatabases/containers/triggers",
                "Microsoft.DocumentDB/databaseAccounts/sqlDatabases/containers/userDefinedFunctions",
                "Microsoft.DocumentDB/databaseAccounts/sqlRoleAssignments",
                "Microsoft.DocumentDB/databaseAccounts/sqlRoleDefinitions",
                "Microsoft.DocumentDB/databaseAccounts/tables",
            ],
            "terraform_types": [
                "azurerm_cosmosdb_account",
                "azurerm_cosmosdb_sql_database",
                "azurerm_cosmosdb_sql_container",
                "azurerm_cosmosdb_sql_function",
                "azurerm_cosmosdb_sql_stored_procedure",
                "azurerm_cosmosdb_sql_trigger",
                "azurerm_cosmosdb_sql_role_assignment",
                "azurerm_cosmosdb_sql_role_definition",
                "azurerm_cosmosdb_sql_dedicated_gateway",
                "azurerm_cosmosdb_cassandra_keyspace",
                "azurerm_cosmosdb_cassandra_table",
                "azurerm_cosmosdb_cassandra_cluster",
                "azurerm_cosmosdb_cassandra_datacenter",
                "azurerm_cosmosdb_gremlin_database",
                "azurerm_cosmosdb_gremlin_graph",
                "azurerm_cosmosdb_mongo_database",
                "azurerm_cosmosdb_mongo_collection",
                "azurerm_cosmosdb_notebook_workspace",
                "azurerm_cosmosdb_table",
            ],
            "bicep_types": [
                "databaseAccount",
                "cosmosDb",
                "cosmos",
                "documentDb",
                "sqlDatabase",
                "sqlContainer",
                "cassandraKeyspace",
                "cassandraTable",
                "gremlinDatabase",
                "gremlinGraph",
                "mongoDatabase",
                "mongoCollection",
                "table",
                "notebookWorkspace",
                "sqlRoleAssignment",
                "sqlRoleDefinition",
            ],
            "keywords": [
                "cosmos db",
                "cosmosdb",
                "documentdb",
                "nosql",
                "globally distributed",
                "multi-model",
                "sql api",
                "core sql",
                "mongodb api",
                "cassandra api",
                "gremlin api",
                "graph api",
                "table api",
                "consistency level",
                "strong consistency",
                "bounded staleness",
                "session consistency",
                "consistent prefix",
                "eventual consistency",
                "partition key",
                "throughput",
                "request units",
                "ru/s",
                "autoscale",
                "manual throughput",
                "provisioned throughput",
                "serverless",
                "global distribution",
                "multi-region",
                "multi-master",
                "multi-write",
                "automatic failover",
                "manual failover",
                "conflict resolution",
                "last write wins",
                "custom conflict resolution",
                "stored procedures",
                "triggers",
                "user defined functions",
                "udf",
                "change feed",
                "time to live",
                "ttl",
                "indexing policy",
                "composite index",
                "spatial index",
                "unique key",
                "analytical store",
                "synapse link",
                "htap",
                "backup",
                "continuous backup",
                "periodic backup",
                "point in time restore",
                "pitr",
                "rbac",
                "data plane rbac",
                "managed identity",
                "private endpoint",
                "ip firewall",
                "virtual network",
                "service endpoint",
                "encryption at rest",
                "customer managed keys",
                "cmk",
                "azure monitor",
                "diagnostic logs",
                "metrics",
                "alerts",
            ],
        },
        "EventHub": {
            "arm_types": [
                "Microsoft.EventHub/namespaces",
                "Microsoft.EventHub/namespaces/authorizationRules",
                "Microsoft.EventHub/namespaces/disasterRecoveryConfigs",
                "Microsoft.EventHub/namespaces/eventhubs",
                "Microsoft.EventHub/namespaces/eventhubs/authorizationRules",
                "Microsoft.EventHub/namespaces/eventhubs/consumergroups",
                "Microsoft.EventHub/namespaces/ipFilterRules",
                "Microsoft.EventHub/namespaces/networkRuleSets",
                "Microsoft.EventHub/namespaces/privateEndpointConnections",
                "Microsoft.EventHub/namespaces/virtualNetworkRules",
                "Microsoft.EventHub/clusters",
                "Microsoft.EventHub/namespaces/schemagroups",
            ],
            "terraform_types": [
                "azurerm_eventhub_namespace",
                "azurerm_eventhub_namespace_authorization_rule",
                "azurerm_eventhub_namespace_customer_managed_key",
                "azurerm_eventhub_namespace_disaster_recovery_config",
                "azurerm_eventhub_namespace_schema_group",
                "azurerm_eventhub",
                "azurerm_eventhub_authorization_rule",
                "azurerm_eventhub_consumer_group",
                "azurerm_eventhub_cluster",
            ],
            "bicep_types": [
                "eventHubNamespace",
                "eventHub",
                "namespace",
                "authorizationRule",
                "consumerGroup",
                "disasterRecoveryConfig",
                "networkRuleSet",
                "privateEndpointConnection",
                "cluster",
                "schemaGroup",
            ],
            "keywords": [
                "event hub",
                "eventhub",
                "event hubs",
                "eventhubs",
                "event streaming",
                "streaming",
                "event ingestion",
                "big data",
                "real-time",
                "namespace",
                "partition",
                "partition key",
                "consumer group",
                "event processor",
                "throughput unit",
                "auto-inflate",
                "capture",
                "event hub capture",
                "avro",
                "schema registry",
                "kafka",
                "kafka endpoint",
                "amqp",
                "authorization rule",
                "shared access signature",
                "sas",
                "connection string",
                "disaster recovery",
                "geo-disaster recovery",
                "geo-dr",
                "paired namespace",
                "failover",
                "dedicated cluster",
                "zone redundancy",
                "availability zones",
                "private endpoint",
                "ip filtering",
                "firewall",
                "virtual network",
                "service endpoint",
                "managed identity",
                "customer managed key",
                "encryption",
                "diagnostic logs",
                "metrics",
                "azure monitor",
                "event hub insights",
                "stream analytics",
                "azure functions",
                "logic apps",
                "data factory",
            ],
        },
        "ServiceBus": {
            "arm_types": [
                "Microsoft.ServiceBus/namespaces",
                "Microsoft.ServiceBus/namespaces/authorizationRules",
                "Microsoft.ServiceBus/namespaces/disasterRecoveryConfigs",
                "Microsoft.ServiceBus/namespaces/ipFilterRules",
                "Microsoft.ServiceBus/namespaces/migrationConfigurations",
                "Microsoft.ServiceBus/namespaces/networkRuleSets",
                "Microsoft.ServiceBus/namespaces/privateEndpointConnections",
                "Microsoft.ServiceBus/namespaces/queues",
                "Microsoft.ServiceBus/namespaces/queues/authorizationRules",
                "Microsoft.ServiceBus/namespaces/topics",
                "Microsoft.ServiceBus/namespaces/topics/authorizationRules",
                "Microsoft.ServiceBus/namespaces/topics/subscriptions",
                "Microsoft.ServiceBus/namespaces/topics/subscriptions/rules",
                "Microsoft.ServiceBus/namespaces/virtualNetworkRules",
            ],
            "terraform_types": [
                "azurerm_servicebus_namespace",
                "azurerm_servicebus_namespace_authorization_rule",
                "azurerm_servicebus_namespace_customer_managed_key",
                "azurerm_servicebus_namespace_disaster_recovery_config",
                "azurerm_servicebus_namespace_network_rule_set",
                "azurerm_servicebus_queue",
                "azurerm_servicebus_queue_authorization_rule",
                "azurerm_servicebus_topic",
                "azurerm_servicebus_topic_authorization_rule",
                "azurerm_servicebus_subscription",
                "azurerm_servicebus_subscription_rule",
            ],
            "bicep_types": [
                "serviceBusNamespace",
                "serviceBus",
                "namespace",
                "queue",
                "topic",
                "subscription",
                "authorizationRule",
                "disasterRecoveryConfig",
                "networkRuleSet",
                "privateEndpointConnection",
                "subscriptionRule",
            ],
            "keywords": [
                "service bus",
                "servicebus",
                "messaging",
                "message queue",
                "queue",
                "topic",
                "subscription",
                "enterprise messaging",
                "message broker",
                "publish subscribe",
                "pub/sub",
                "dead letter queue",
                "dlq",
                "session",
                "message session",
                "fifo",
                "first in first out",
                "duplicate detection",
                "message deferral",
                "scheduled messages",
                "message batching",
                "transaction",
                "amqp",
                "authorization rule",
                "shared access signature",
                "sas",
                "connection string",
                "premium tier",
                "standard tier",
                "basic tier",
                "messaging unit",
                "namespace",
                "geo-disaster recovery",
                "geo-dr",
                "paired namespace",
                "failover",
                "zone redundancy",
                "availability zones",
                "private endpoint",
                "ip filtering",
                "firewall",
                "virtual network",
                "service endpoint",
                "managed identity",
                "customer managed key",
                "encryption",
                "diagnostic logs",
                "metrics",
                "azure monitor",
                "service bus explorer",
                "azure functions",
                "logic apps",
                "event grid",
            ],
        },
        "DataFactory": {
            "arm_types": [
                "Microsoft.DataFactory/factories",
                "Microsoft.DataFactory/factories/datasets",
                "Microsoft.DataFactory/factories/dataflows",
                "Microsoft.DataFactory/factories/integrationRuntimes",
                "Microsoft.DataFactory/factories/linkedservices",
                "Microsoft.DataFactory/factories/managedVirtualNetworks",
                "Microsoft.DataFactory/factories/managedVirtualNetworks/managedPrivateEndpoints",
                "Microsoft.DataFactory/factories/pipelines",
                "Microsoft.DataFactory/factories/privateEndpointConnections",
                "Microsoft.DataFactory/factories/triggers",
            ],
            "terraform_types": [
                "azurerm_data_factory",
                "azurerm_data_factory_custom_dataset",
                "azurerm_data_factory_data_flow",
                "azurerm_data_factory_dataset_azure_blob",
                "azurerm_data_factory_dataset_azure_sql_table",
                "azurerm_data_factory_dataset_cosmosdb_sqlapi",
                "azurerm_data_factory_dataset_delimited_text",
                "azurerm_data_factory_dataset_http",
                "azurerm_data_factory_dataset_json",
                "azurerm_data_factory_dataset_mysql",
                "azurerm_data_factory_dataset_parquet",
                "azurerm_data_factory_dataset_postgresql",
                "azurerm_data_factory_dataset_snowflake",
                "azurerm_data_factory_dataset_sql_server_table",
                "azurerm_data_factory_integration_runtime_azure",
                "azurerm_data_factory_integration_runtime_azure_ssis",
                "azurerm_data_factory_integration_runtime_self_hosted",
                "azurerm_data_factory_linked_custom_service",
                "azurerm_data_factory_linked_service_azure_blob_storage",
                "azurerm_data_factory_linked_service_azure_databricks",
                "azurerm_data_factory_linked_service_azure_file_storage",
                "azurerm_data_factory_linked_service_azure_function",
                "azurerm_data_factory_linked_service_azure_search",
                "azurerm_data_factory_linked_service_azure_sql_database",
                "azurerm_data_factory_linked_service_azure_table_storage",
                "azurerm_data_factory_linked_service_cosmosdb",
                "azurerm_data_factory_linked_service_cosmosdb_mongoapi",
                "azurerm_data_factory_linked_service_data_lake_storage_gen2",
                "azurerm_data_factory_linked_service_key_vault",
                "azurerm_data_factory_linked_service_kusto",
                "azurerm_data_factory_linked_service_mysql",
                "azurerm_data_factory_linked_service_odata",
                "azurerm_data_factory_linked_service_odbc",
                "azurerm_data_factory_linked_service_postgresql",
                "azurerm_data_factory_linked_service_sftp",
                "azurerm_data_factory_linked_service_snowflake",
                "azurerm_data_factory_linked_service_sql_server",
                "azurerm_data_factory_linked_service_synapse",
                "azurerm_data_factory_linked_service_web",
                "azurerm_data_factory_managed_private_endpoint",
                "azurerm_data_factory_pipeline",
                "azurerm_data_factory_trigger_blob_event",
                "azurerm_data_factory_trigger_custom_event",
                "azurerm_data_factory_trigger_schedule",
                "azurerm_data_factory_trigger_tumbling_window",
            ],
            "bicep_types": [
                "dataFactory",
                "factory",
                "adf",
                "dataset",
                "dataflow",
                "integrationRuntime",
                "linkedService",
                "managedVirtualNetwork",
                "managedPrivateEndpoint",
                "pipeline",
                "trigger",
            ],
            "keywords": [
                "data factory",
                "adf",
                "azure data factory",
                "etl",
                "elt",
                "data integration",
                "data pipeline",
                "pipeline",
                "activity",
                "dataset",
                "linked service",
                "integration runtime",
                "self-hosted integration runtime",
                "shir",
                "azure integration runtime",
                "ssis integration runtime",
                "data flow",
                "mapping data flow",
                "wrangling data flow",
                "copy activity",
                "data movement",
                "transformation",
                "trigger",
                "schedule trigger",
                "tumbling window trigger",
                "event trigger",
                "storage event trigger",
                "custom event trigger",
                "parameter",
                "variable",
                "expression",
                "dynamic content",
                "managed virtual network",
                "managed private endpoint",
                "git integration",
                "ci/cd",
                "devops",
                "source control",
                "arm template",
                "monitoring",
                "alerts",
                "metrics",
                "diagnostic logs",
                "data lineage",
                "purview",
                "managed identity",
                "key vault",
                "encryption",
                "customer managed key",
            ],
        },
        "Databricks": {
            "arm_types": [
                "Microsoft.Databricks/workspaces",
                "Microsoft.Databricks/workspaces/privateEndpointConnections",
                "Microsoft.Databricks/workspaces/virtualNetworkPeerings",
            ],
            "terraform_types": [
                "azurerm_databricks_workspace",
                "azurerm_databricks_workspace_customer_managed_key",
                "azurerm_databricks_access_connector",
                "azurerm_databricks_virtual_network_peering",
            ],
            "bicep_types": [
                "databricksWorkspace",
                "databricks",
                "workspace",
                "privateEndpointConnection",
                "virtualNetworkPeering",
            ],
            "keywords": [
                "databricks",
                "azure databricks",
                "spark",
                "apache spark",
                "workspace",
                "cluster",
                "notebook",
                "job",
                "library",
                "dbfs",
                "databricks file system",
                "delta lake",
                "delta table",
                "unity catalog",
                "mlflow",
                "machine learning",
                "ml",
                "data science",
                "data engineering",
                "streaming",
                "batch processing",
                "vnet injection",
                "secure cluster connectivity",
                "no public ip",
                "npip",
                "private link",
                "managed identity",
                "azure ad",
                "scim",
                "token",
                "pat",
                "personal access token",
                "secret scope",
                "key vault backed",
                "encryption",
                "customer managed key",
                "cmk",
                "diagnostic logs",
                "audit logs",
                "workspace analytics",
            ],
        },
        "Synapse": {
            "arm_types": [
                "Microsoft.Synapse/workspaces",
                "Microsoft.Synapse/workspaces/administrators",
                "Microsoft.Synapse/workspaces/auditingSettings",
                "Microsoft.Synapse/workspaces/bigDataPools",
                "Microsoft.Synapse/workspaces/databases",
                "Microsoft.Synapse/workspaces/dedicatedSQLminimalTlsSettings",
                "Microsoft.Synapse/workspaces/encryptionProtector",
                "Microsoft.Synapse/workspaces/eventGridFilters",
                "Microsoft.Synapse/workspaces/extendedAuditingSettings",
                "Microsoft.Synapse/workspaces/firewallRules",
                "Microsoft.Synapse/workspaces/integrationRuntimes",
                "Microsoft.Synapse/workspaces/keys",
                "Microsoft.Synapse/workspaces/kustoPools",
                "Microsoft.Synapse/workspaces/kustoPools/attachedDatabaseConfigurations",
                "Microsoft.Synapse/workspaces/kustoPools/databases",
                "Microsoft.Synapse/workspaces/kustoPools/databases/dataConnections",
                "Microsoft.Synapse/workspaces/kustoPools/databases/principalAssignments",
                "Microsoft.Synapse/workspaces/kustoPools/principalAssignments",
                "Microsoft.Synapse/workspaces/libraries",
                "Microsoft.Synapse/workspaces/linkedServices",
                "Microsoft.Synapse/workspaces/managedIdentitySqlControlSettings",
                "Microsoft.Synapse/workspaces/privateEndpointConnections",
                "Microsoft.Synapse/workspaces/privateLinkHubs",
                "Microsoft.Synapse/workspaces/securityAlertPolicies",
                "Microsoft.Synapse/workspaces/sparkConfigurations",
                "Microsoft.Synapse/workspaces/sqlAdministrators",
                "Microsoft.Synapse/workspaces/sqlPools",
                "Microsoft.Synapse/workspaces/sqlPools/auditingSettings",
                "Microsoft.Synapse/workspaces/sqlPools/connectionPolicies",
                "Microsoft.Synapse/workspaces/sqlPools/dataMaskingPolicies",
                "Microsoft.Synapse/workspaces/sqlPools/dataMaskingPolicies/rules",
                "Microsoft.Synapse/workspaces/sqlPools/dataWarehouseUserActivities",
                "Microsoft.Synapse/workspaces/sqlPools/encryptionProtector",
                "Microsoft.Synapse/workspaces/sqlPools/extendedAuditingSettings",
                "Microsoft.Synapse/workspaces/sqlPools/geoBackupPolicies",
                "Microsoft.Synapse/workspaces/sqlPools/maintenanceWindows",
                "Microsoft.Synapse/workspaces/sqlPools/metadataSync",
                "Microsoft.Synapse/workspaces/sqlPools/replicationLinks",
                "Microsoft.Synapse/workspaces/sqlPools/restorePoints",
                "Microsoft.Synapse/workspaces/sqlPools/schemas",
                "Microsoft.Synapse/workspaces/sqlPools/schemas/tables",
                "Microsoft.Synapse/workspaces/sqlPools/schemas/tables/columns",
                "Microsoft.Synapse/workspaces/sqlPools/schemas/tables/columns/sensitivityLabels",
                "Microsoft.Synapse/workspaces/sqlPools/securityAlertPolicies",
                "Microsoft.Synapse/workspaces/sqlPools/transparentDataEncryption",
                "Microsoft.Synapse/workspaces/sqlPools/vulnerabilityAssessments",
                "Microsoft.Synapse/workspaces/sqlPools/vulnerabilityAssessments/rules/baselines",
                "Microsoft.Synapse/workspaces/sqlPools/workloadGroups",
                "Microsoft.Synapse/workspaces/sqlPools/workloadGroups/workloadClassifiers",
                "Microsoft.Synapse/workspaces/vulnerabilityAssessments",
            ],
            "terraform_types": [
                "azurerm_synapse_workspace",
                "azurerm_synapse_workspace_aad_admin",
                "azurerm_synapse_workspace_extended_auditing_policy",
                "azurerm_synapse_workspace_key",
                "azurerm_synapse_workspace_security_alert_policy",
                "azurerm_synapse_workspace_sql_aad_admin",
                "azurerm_synapse_workspace_vulnerability_assessment",
                "azurerm_synapse_firewall_rule",
                "azurerm_synapse_integration_runtime_azure",
                "azurerm_synapse_integration_runtime_self_hosted",
                "azurerm_synapse_linked_service",
                "azurerm_synapse_managed_private_endpoint",
                "azurerm_synapse_private_link_hub",
                "azurerm_synapse_role_assignment",
                "azurerm_synapse_spark_pool",
                "azurerm_synapse_sql_pool",
                "azurerm_synapse_sql_pool_extended_auditing_policy",
                "azurerm_synapse_sql_pool_security_alert_policy",
                "azurerm_synapse_sql_pool_workload_classifier",
                "azurerm_synapse_sql_pool_workload_group",
            ],
            "bicep_types": [
                "synapseWorkspace",
                "synapse",
                "workspace",
                "sqlPool",
                "sparkPool",
                "bigDataPool",
                "kustoPool",
                "integrationRuntime",
                "linkedService",
                "firewall",
                "firewallRule",
                "privateEndpointConnection",
                "privateLinkHub",
                "administrator",
                "auditingSettings",
                "encryptionProtector",
                "securityAlertPolicy",
                "vulnerabilityAssessment",
                "workloadGroup",
                "workloadClassifier",
            ],
            "keywords": [
                "synapse",
                "azure synapse",
                "synapse analytics",
                "sql pool",
                "dedicated sql pool",
                "serverless sql pool",
                "spark pool",
                "apache spark",
                "data warehouse",
                "data lake",
                "big data",
                "analytics",
                "workspace",
                "synapse studio",
                "pipeline",
                "data flow",
                "notebook",
                "sql script",
                "spark job",
                "integration runtime",
                "linked service",
                "dataset",
                "data explorer",
                "kusto pool",
                "power bi",
                "purview",
                "data lineage",
                "managed virtual network",
                "managed private endpoint",
                "firewall",
                "ip firewall",
                "azure ad",
                "managed identity",
                "sql authentication",
                "encryption",
                "transparent data encryption",
                "tde",
                "column level security",
                "row level security",
                "dynamic data masking",
                "auditing",
                "threat detection",
                "vulnerability assessment",
                "workload management",
                "workload isolation",
                "result set caching",
                "materialized views",
                "replicated tables",
                "distribution",
                "partition",
                "polybase",
                "copy statement",
                "external table",
                "openrowset",
                "delta lake",
                "parquet",
                "monitoring",
                "dmv",
                "dynamic management views",
            ],
        },
        "LogicApps": {
            "arm_types": [
                "Microsoft.Logic/workflows",
                "Microsoft.Logic/integrationAccounts",
                "Microsoft.Logic/integrationAccounts/agreements",
                "Microsoft.Logic/integrationAccounts/assemblies",
                "Microsoft.Logic/integrationAccounts/batchConfigurations",
                "Microsoft.Logic/integrationAccounts/certificates",
                "Microsoft.Logic/integrationAccounts/groups",
                "Microsoft.Logic/integrationAccounts/maps",
                "Microsoft.Logic/integrationAccounts/partners",
                "Microsoft.Logic/integrationAccounts/rosettanetprocessconfigurations",
                "Microsoft.Logic/integrationAccounts/schemas",
                "Microsoft.Logic/integrationAccounts/sessions",
                "Microsoft.Logic/integrationServiceEnvironments",
                "Microsoft.Logic/integrationServiceEnvironments/managedApis",
                "Microsoft.Logic/isolatedEnvironments",
                "Microsoft.Logic/workflows/accessKeys",
                "Microsoft.Logic/workflows/runs",
                "Microsoft.Logic/workflows/runs/actions",
                "Microsoft.Logic/workflows/runs/actions/repetitions",
                "Microsoft.Logic/workflows/runs/actions/repetitions/requestHistories",
                "Microsoft.Logic/workflows/runs/actions/requestHistories",
                "Microsoft.Logic/workflows/runs/actions/scopeRepetitions",
                "Microsoft.Logic/workflows/runs/operations",
                "Microsoft.Logic/workflows/triggers",
                "Microsoft.Logic/workflows/versions",
                "Microsoft.Logic/workflows/versions/triggers",
            ],
            "terraform_types": [
                "azurerm_logic_app_workflow",
                "azurerm_logic_app_action_custom",
                "azurerm_logic_app_action_http",
                "azurerm_logic_app_integration_account",
                "azurerm_logic_app_integration_account_agreement",
                "azurerm_logic_app_integration_account_assembly",
                "azurerm_logic_app_integration_account_batch_configuration",
                "azurerm_logic_app_integration_account_certificate",
                "azurerm_logic_app_integration_account_map",
                "azurerm_logic_app_integration_account_partner",
                "azurerm_logic_app_integration_account_schema",
                "azurerm_logic_app_integration_account_session",
                "azurerm_logic_app_trigger_custom",
                "azurerm_logic_app_trigger_http_request",
                "azurerm_logic_app_trigger_recurrence",
            ],
            "bicep_types": [
                "logicApp",
                "workflow",
                "workflows",
                "integrationAccount",
                "integrationAccounts",
                "integrationServiceEnvironment",
                "ise",
                "agreement",
                "assembly",
                "batchConfiguration",
                "certificate",
                "map",
                "partner",
                "schema",
                "session",
                "trigger",
                "action",
                "run",
            ],
            "keywords": [
                "logic apps",
                "logic app",
                "workflow",
                "integration",
                "business process",
                "automation",
                "connector",
                "trigger",
                "action",
                "condition",
                "loop",
                "switch",
                "scope",
                "parallel branch",
                "http trigger",
                "recurrence trigger",
                "request trigger",
                "webhook",
                "integration account",
                "b2b",
                "edi",
                "xml",
                "json",
                "flat file",
                "transform",
                "map",
                "schema",
                "agreement",
                "partner",
                "certificate",
                "assembly",
                "batch",
                "session",
                "integration service environment",
                "ise",
                "isolated",
                "premium",
                "consumption",
                "standard",
                "stateful",
                "stateless",
                "managed connector",
                "custom connector",
                "api connection",
                "connection",
                "authentication",
                "oauth",
                "managed identity",
                "service principal",
                "run history",
                "monitoring",
                "diagnostic logs",
                "metrics",
                "alerts",
                "error handling",
                "retry policy",
                "timeout",
                "concurrency",
                "throttling",
            ],
        },
        "AzureFirewall": {
            "arm_types": [
                "Microsoft.Network/azureFirewalls",
                "Microsoft.Network/firewallPolicies",
                "Microsoft.Network/firewallPolicies/ruleCollectionGroups",
                "Microsoft.Network/azureFirewalls/applicationRuleCollections",
                "Microsoft.Network/azureFirewalls/natRuleCollections",
                "Microsoft.Network/azureFirewalls/networkRuleCollections",
                "Microsoft.Network/publicIPAddresses",
                "Microsoft.Network/publicIPPrefixes",
            ],
            "terraform_types": [
                "azurerm_firewall",
                "azurerm_firewall_policy",
                "azurerm_firewall_policy_rule_collection_group",
                "azurerm_firewall_application_rule_collection",
                "azurerm_firewall_nat_rule_collection",
                "azurerm_firewall_network_rule_collection",
                "azurerm_public_ip",
                "azurerm_public_ip_prefix",
            ],
            "bicep_types": [
                "azureFirewall",
                "firewall",
                "firewallPolicy",
                "firewallPolicies",
                "ruleCollectionGroups",
                "applicationRuleCollections",
                "natRuleCollections",
                "networkRuleCollections",
                "publicIPAddress",
                "publicIPPrefix",
            ],
            "keywords": [
                "azure firewall",
                "firewall",
                "firewall policy",
                "rule collection",
                "application rule",
                "network rule",
                "nat rule",
                "dnat",
                "snat",
                "threat intelligence",
                "idps",
                "intrusion detection",
                "firewall logs",
                "firewall metrics",
                "hub spoke",
                "forced tunneling",
                "premium firewall",
                "standard firewall",
                "basic firewall",
                "firewall manager",
                "secure hub",
                "virtual wan",
                "availability zones",
                "dns proxy",
                "fqdn filtering",
                "url filtering",
                "web categories",
                "tls inspection",
            ],
        },
        "Identity": {
            "arm_types": [
                "Microsoft.ManagedIdentity/userAssignedIdentities",
                "Microsoft.ManagedIdentity/identities",
                "Microsoft.Authorization/roleAssignments",
                "Microsoft.Authorization/roleDefinitions",
                "Microsoft.Authorization/policyAssignments",
                "Microsoft.Authorization/policyDefinitions",
                "Microsoft.Authorization/policySetDefinitions",
                "Microsoft.AAD/domainServices",
                "Microsoft.AzureActiveDirectory/b2cDirectories",
            ],
            "terraform_types": [
                "azurerm_user_assigned_identity",
                "azurerm_role_assignment",
                "azurerm_role_definition",
                "azurerm_policy_assignment",
                "azurerm_policy_definition",
                "azurerm_policy_set_definition",
                "azuread_user",
                "azuread_group",
                "azuread_application",
                "azuread_service_principal",
                "azuread_application_password",
                "azuread_application_certificate",
                "azuread_conditional_access_policy",
                "azurerm_active_directory_domain_service",
            ],
            "bicep_types": [
                "userAssignedIdentity",
                "identity",
                "roleAssignment",
                "roleDefinition",
                "policyAssignment",
                "policyDefinition",
                "policySetDefinition",
                "managedIdentity",
                "principalId",
                "tenantId",
                "objectId",
            ],
            "keywords": [
                "managed identity",
                "user assigned identity",
                "system assigned identity",
                "service principal",
                "role assignment",
                "rbac",
                "role based access control",
                "azure ad",
                "azure active directory",
                "identity",
                "authentication",
                "authorization",
                "principal id",
                "object id",
                "tenant id",
                "conditional access",
                "privileged identity",
                "pim",
                "access review",
                "identity governance",
                "azure ad b2c",
                "azure ad ds",
                "domain services",
                "application registration",
                "app registration",
                "enterprise application",
                "multi-factor authentication",
                "mfa",
                "single sign on",
                "sso",
                "federation",
                "saml",
                "oauth",
                "openid connect",
                "oidc",
                "jwt",
                "claims",
                "token",
                "directory sync",
                "azure ad connect",
                "hybrid identity",
                "password hash sync",
                "pass through authentication",
                "pta",
                "seamless sso",
            ],
        },
    }

    def __init__(
        self,
        repo_id: str = None,
        pr_id: int = None,
        local_folder: str = None,
        full_coverage_output: str = None,
        create_pr: bool = False,
    ):
        """Initialize the security PR reviewer.

        Args:
            repo_id: Repository ID for PR mode
            pr_id: Pull Request ID for PR mode
            local_folder: Local folder path for local analysis mode
            create_pr: Whether to create a new PR with security recommendations
        """
        # Load environment variables - search in multiple places
        self._load_environment()

        # Determine operating mode
        if local_folder:
            self.mode = "local"
            self.local_folder = local_folder
            self.repo_id = None
            self.pr_id = None
            self.create_pr = False
        elif repo_id and pr_id:
            self.mode = "pr"
            self.repo_id = repo_id
            self.pr_id = pr_id
            self.local_folder = None
            self.create_pr = False
        elif repo_id and create_pr:
            self.mode = "create_pr"
            self.repo_id = repo_id
            self.pr_id = None
            self.local_folder = None
            self.create_pr = True
        else:
            raise ValueError(
                "Either provide (repo_id and pr_id) for PR mode, local_folder for local analysis mode, or (repo_id and create_pr=True) for PR creation mode"
            )

        # Initialize clients and benchmark data
        if self.mode in ["pr", "create_pr"]:
            self.ado_connection = self._init_ado_client()
        else:
            self.ado_connection = None
        self.openai_client = self._init_openai_client()
        self.benchmark_data = None

        # Store branch and PR creation details for create_pr mode
        self.created_branch_name = None
        self.created_pr_id = None

        logger.info(f"SecurityPRReviewer initialized in {self.mode} mode")

    def _get_git_client(self):
        """Get Git client with fallback for different SDK versions."""
        git_client = None

        # Try different SDK versions in order of preference
        sdk_versions = [
            "azure.devops.v7_0.git.git_client.GitClient",
            "azure.devops.v6_0.git.git_client.GitClient",
            "azure.devops.v5_1.git.git_client.GitClient",
            "azure.devops.v5_0.git.git_client.GitClient"
        ]

        for sdk_version in sdk_versions:
            try:
                git_client = self.ado_connection.get_client(sdk_version)
                logger.info(f"Successfully initialized Git client with {sdk_version}")
                break
            except (ModuleNotFoundError, AttributeError, ImportError) as e:
                logger.debug(f"Failed to get client with {sdk_version}: {str(e)}")
                continue

        # Fallback methods if versioned approach fails
        if not git_client:
            try:
                # Try the legacy get_git_client method
                git_client = self.ado_connection.get_git_client()
                logger.info("Successfully initialized Git client with legacy method")
            except AttributeError:
                try:
                    # Try string-based approach
                    git_client = self.ado_connection.get_client('git')
                    logger.info("Successfully initialized Git client with string method")
                except Exception as e:
                    logger.error(f"All Git client initialization methods failed: {str(e)}")

        if not git_client:
            raise ValueError("Failed to get Git client from Azure DevOps SDK. Please check your azure-devops package version.")

        return git_client

    def _load_environment(self):
        """Load environment variables from .env file, searching in multiple locations."""
        # Try to find .env file
        env_path = find_dotenv(usecwd=True)  # First try in cwd

        if not env_path:
            # Check for .env in script directory
            script_dir = Path(__file__).resolve().parent
            script_env = script_dir / ".env"

            if script_env.exists():
                env_path = str(script_env)
                logger.info(f"Found .env file in script directory: {env_path}")

        if env_path:
            logger.info(f"Loading environment variables from: {env_path}")
            load_dotenv(env_path, override=True)
        else:
            logger.warning(
                "No .env file found. Relying on environment variables already set or defaults."
            )

        # Log available environment variables (without showing actual values)
        env_vars = {
            "Azure DevOps": [
                "AZURE_DEVOPS_PAT",
                "AZURE_DEVOPS_ORG",
                "AZURE_DEVOPS_PROJECT",
            ],
            "Azure OpenAI": [
                "AZURE_OPENAI_ENDPOINT",
                "AZURE_OPENAI_API_KEY",
                "AZURE_OPENAI_DEPLOYMENT",
                "AZURE_OPENAI_API_VERSION",
                "AZURE_OPENAI_USE_AD_AUTH",
            ],
        }

        for category, vars in env_vars.items():
            logger.info(f"Available {category} environment variables:")
            for var in vars:
                value = os.environ.get(var)
                if value:
                    # Show first few characters of value for debugging but hide sensitive data
                    if var.endswith("_PAT") or var.endswith("_KEY"):
                        display = (
                            f"{value[:3]}...{value[-3:]}" if len(value) > 10 else "***"
                        )
                    else:
                        display = value
                    logger.info(f"  - {var}: {display}")
                else:
                    logger.warning(f"  - {var}: Not set")

    def _init_ado_client(self) -> Connection:
        """Initialize Azure DevOps client."""
        pat = os.environ.get("AZURE_DEVOPS_PAT")
        org_url = os.environ.get("AZURE_DEVOPS_ORG")

        if not pat or not org_url:
            raise ValueError(
                "Environment variables AZURE_DEVOPS_PAT and AZURE_DEVOPS_ORG must be set"
            )

        # Convert org name to URL if it's not already a URL
        if not org_url.startswith("https://"):
            org_url = f"https://dev.azure.com/{org_url}"

        logger.info(f"Initializing Azure DevOps client for {org_url}")
        return Connection(base_url=org_url, creds=BasicAuthentication("", pat))

    def _init_openai_client(self):
        """
        Initialize Azure OpenAI client using the official sample approach.

        Based on: https://github.com/Azure/azure-sdk-for-python/blob/main/sdk/openai/azure-openai/samples/chat_completions_aoai_quickstart.py

        Supports both API key and Azure AD token-based authentication.
        """
        # Get required configuration
        endpoint = os.environ.get("AZURE_OPENAI_ENDPOINT")
        api_key = os.environ.get("AZURE_OPENAI_API_KEY")
        api_version = os.environ.get("AZURE_OPENAI_API_VERSION", "2024-02-01")

        if not endpoint:
            raise ValueError("Environment variable AZURE_OPENAI_ENDPOINT must be set")

        logger.info(f"Initializing Azure OpenAI client with endpoint: {endpoint}")

        # Try to use Azure AD authentication first (more secure for production)
        use_azure_ad = os.environ.get("AZURE_OPENAI_USE_AD_AUTH", "").lower() == "true"

        if use_azure_ad:
            try:
                logger.info("Attempting to use Azure AD authentication")
                from azure.identity import (
                    DefaultAzureCredential,
                    get_bearer_token_provider,
                )

                token_provider = get_bearer_token_provider(
                    DefaultAzureCredential(),
                    "https://cognitiveservices.azure.com/.default",
                )

                return AzureOpenAI(
                    azure_endpoint=endpoint,
                    api_version=api_version,
                    azure_ad_token_provider=token_provider,
                )
            except Exception as e:
                logger.warning(
                    f"Failed to use Azure AD authentication: {e}. Falling back to API key."
                )

        # Fall back to API key authentication
        if not api_key:
            raise ValueError(
                "Either API key or Azure AD authentication must be configured"
            )

        logger.info("Using API key authentication")
        return AzureOpenAI(
            azure_endpoint=endpoint, api_key=api_key, api_version=api_version
        )

    def prepare_benchmark(self) -> Dict:
        """
        Download and prepare the Azure Security Benchmark from official source.

        This method downloads the Excel file, converts it to JSON, and stores
        it in a dedicated folder for future use.
        """
        try:
            logger.info("Preparing Azure Security Benchmark data")

            # Create directory for benchmark files if it doesn't exist
            benchmark_dir = Path("SecurityBenchmarks")
            benchmark_dir.mkdir(exist_ok=True)

            # Set paths for benchmark files
            excel_path = benchmark_dir / "Azure_Security_Benchmark_v3.xlsx"
            json_path = benchmark_dir / "Azure_Security_Benchmark_v3.json"

            # Check if JSON already exists, otherwise download and convert
            if json_path.exists():
                logger.info(f"Loading benchmark from existing JSON file: {json_path}")
                with open(json_path, "r") as f:
                    self.benchmark_data = json.load(f)
            else:
                # Download benchmark if Excel doesn't exist
                if not excel_path.exists():
                    self._download_benchmark_file(excel_path)

                # Convert Excel to JSON
                logger.info(f"Converting benchmark from Excel to JSON")
                self.benchmark_data = self._convert_benchmark_to_json(
                    excel_path, json_path
                )

            logger.info(
                f"Prepared benchmark data with {len(self.benchmark_data['controls'])} controls"
            )
            logger.debug(
                f"------------------------------------------------------------"
            )
            logger.debug(f"Benchmark data: {self.benchmark_data}")
            return self.benchmark_data

        except Exception as e:
            logger.error(f"Error preparing benchmark data: {e}")
            logger.info("Falling back to simplified benchmark data")

            # Create a simplified benchmark as fallback
            # self.benchmark_data = {
            #     "controls": [
            #         {
            #             "id": "NS-1",
            #             "name": "Protect resources using network security groups or Azure Firewall",
            #             "domain": "Network Security",
            #             "description": "Protect sensitive or critical resources with network security groups or Azure Firewall. Resources that require attention include Storage Accounts, Key Vaults, and SQL databases."
            #         },
            #         {
            #             "id": "NS-2",
            #             "name": "Protect public endpoints",
            #             "domain": "Network Security",
            #             "description": "Secure all public endpoints of your Azure resources to minimize data exposure. This includes restricting network access, using service endpoints and private endpoints where possible."
            #         },
            #         {
            #             "id": "DP-1",
            #             "name": "Enable encryption at rest",
            #             "domain": "Data Protection",
            #             "description": "Enable encryption at rest for all Azure resources handling sensitive data. Use Azure Key Vault to manage and control access to encryption keys."
            #         },
            #         {
            #             "id": "DP-2",
            #             "name": "Enable encryption in transit",
            #             "domain": "Data Protection",
            #             "description": "Enable secure transfer for all Azure resources handling data. Use TLS 1.2 or later for all traffic and disable older protocols."
            #         },
            #         {
            #             "id": "DP-3",
            #             "name": "Manage sensitive information disclosure",
            #             "domain": "Data Protection",
            #             "description": "Protect sensitive information such as access keys, connection strings, and certificates by storing them in Azure Key Vault."
            #         }
            #     ]
            # }
            self.benchmark_data = {
                "controls": [
                    # Identity Management (IM-1 to IM-9)
                    {
                        "id": "IM-1",
                        "name": "Use Azure Active Directory for Identity Management",
                        "domain": "Identity Management",
                        "description": "Leverage Azure AD for secure identity and access management.",
                    },
                    {
                        "id": "IM-2",
                        "name": "Enable Multi-Factor Authentication (MFA)",
                        "domain": "Identity Management",
                        "description": "Ensure all users and administrators use MFA for secure access.",
                    },
                    {
                        "id": "IM-3",
                        "name": "Use Conditional Access Policies",
                        "domain": "Identity Management",
                        "description": "Implement conditional access policies to enforce secure access.",
                    },
                    {
                        "id": "IM-4",
                        "name": "Review and Manage User Accounts",
                        "domain": "Identity Management",
                        "description": "Regularly review and manage user accounts, especially privileged accounts.",
                    },
                    {
                        "id": "IM-5",
                        "name": "Monitor Identity and Access Activities",
                        "domain": "Identity Management",
                        "description": "Enable and review logs to monitor identity-related activities.",
                    },
                    {
                        "id": "IM-6",
                        "name": "Use Role-Based Access Control (RBAC)",
                        "domain": "Identity Management",
                        "description": "Assign access rights using RBAC to limit privileges to what is necessary.",
                    },
                    {
                        "id": "IM-7",
                        "name": "Secure Application Identities",
                        "domain": "Identity Management",
                        "description": "Protect application identities and limit their permissions.",
                    },
                    {
                        "id": "IM-8",
                        "name": "Use Managed Identities for Azure Resources",
                        "domain": "Identity Management",
                        "description": "Use managed identities for secure resource-to-resource authentication.",
                    },
                    {
                        "id": "IM-9",
                        "name": "Review and Manage External Collaborators",
                        "domain": "Identity Management",
                        "description": "Regularly review and manage access for external users.",
                    },
                    # Network Security (NS-1 to NS-10)
                    {
                        "id": "NS-1",
                        "name": "Protect resources using network security groups or Azure Firewall",
                        "domain": "Network Security",
                        "description": "Use network security groups (NSGs) or Azure Firewall to protect sensitive or critical resources. Resources that require attention include Storage Accounts, Key Vaults, and SQL databases.",
                    },
                    {
                        "id": "NS-2",
                        "name": "Protect public endpoints",
                        "domain": "Network Security",
                        "description": "Secure all public endpoints to minimize exposure.",
                    },
                    {
                        "id": "NS-3",
                        "name": "Use Network Security Groups (NSGs)",
                        "domain": "Network Security",
                        "description": "Implement NSGs to control inbound and outbound traffic.",
                    },
                    {
                        "id": "NS-4",
                        "name": "Use Azure Firewall or third-party firewall",
                        "domain": "Network Security",
                        "description": "Protect resources with Azure Firewall or a third-party firewall solution.",
                    },
                    {
                        "id": "NS-5",
                        "name": "Use Private Endpoints",
                        "domain": "Network Security",
                        "description": "Implement private endpoints to securely access resources.",
                    },
                    {
                        "id": "NS-6",
                        "name": "Use Virtual Network Service Endpoints",
                        "domain": "Network Security",
                        "description": "Use service endpoints to secure traffic to Azure services.",
                    },
                    {
                        "id": "NS-7",
                        "name": "Restrict Traffic with Just-in-Time VM Access",
                        "domain": "Network Security",
                        "description": "Enable JIT VM access to reduce exposure of management ports.",
                    },
                    {
                        "id": "NS-8",
                        "name": "Implement DDoS Protection",
                        "domain": "Network Security",
                        "description": "Enable DDoS Protection to safeguard resources.",
                    },
                    {
                        "id": "NS-9",
                        "name": "Monitor Network Traffic",
                        "domain": "Network Security",
                        "description": "Use Azure Monitor and Network Watcher to monitor and log traffic.",
                    },
                    {
                        "id": "NS-10",
                        "name": "Use Bastion Hosts for Secure VM Access",
                        "domain": "Network Security",
                        "description": "Use Azure Bastion to securely manage VMs without exposing SSH/RDP ports.",
                    },
                    # Access Management (AM-1 to AM-5)
                    {
                        "id": "AM-1",
                        "name": "Assign Least Privilege Access",
                        "domain": "Access Management",
                        "description": "Limit user and application permissions to what is required.",
                    },
                    {
                        "id": "AM-2",
                        "name": "Regularly Review Access Rights",
                        "domain": "Access Management",
                        "description": "Conduct periodic reviews of access assignments.",
                    },
                    {
                        "id": "AM-3",
                        "name": "Implement Privileged Identity Management (PIM)",
                        "domain": "Access Management",
                        "description": "Use PIM to manage and control privileged access.",
                    },
                    {
                        "id": "AM-4",
                        "name": "Use Access Reviews",
                        "domain": "Access Management",
                        "description": "Configure access reviews to ensure only necessary access remains.",
                    },
                    {
                        "id": "AM-5",
                        "name": "Enable Logging for Access Management",
                        "domain": "Access Management",
                        "description": "Enable logs to monitor access management activities.",
                    },
                    # Data Protection (DP-1 to DP-8)
                    {
                        "id": "DP-1",
                        "name": "Enable encryption at rest",
                        "domain": "Data Protection",
                        "description": "Use encryption at rest for all data storage.",
                    },
                    {
                        "id": "DP-2",
                        "name": "Enable encryption in transit",
                        "domain": "Data Protection",
                        "description": "Use TLS 1.2+ for all data transfers.",
                    },
                    {
                        "id": "DP-3",
                        "name": "Manage sensitive information disclosure",
                        "domain": "Data Protection",
                        "description": "Store sensitive data like keys in Azure Key Vault.",
                    },
                    {
                        "id": "DP-4",
                        "name": "Use Managed Disks with Encryption",
                        "domain": "Data Protection",
                        "description": "Ensure managed disks are encrypted.",
                    },
                    {
                        "id": "DP-5",
                        "name": "Backup and Recovery",
                        "domain": "Data Protection",
                        "description": "Implement backup and recovery strategies for critical data.",
                    },
                    {
                        "id": "DP-6",
                        "name": "Secure Data with Customer-Managed Keys (CMK)",
                        "domain": "Data Protection",
                        "description": "Use CMK to control encryption keys for sensitive data.",
                    },
                    {
                        "id": "DP-7",
                        "name": "Use Data Loss Prevention (DLP) Policies",
                        "domain": "Data Protection",
                        "description": "Apply DLP policies to prevent data exfiltration.",
                    },
                    {
                        "id": "DP-8",
                        "name": "Protect Data in Use",
                        "domain": "Data Protection",
                        "description": "Use confidential computing and trusted execution environments.",
                    },
                ]
            }

            logger.info(
                f"Created simplified benchmark data with {len(self.benchmark_data['controls'])} controls"
            )
            return self.benchmark_data

    def _download_benchmark_file(self, file_path: Path) -> None:
        """Download the Azure Security Benchmark Excel file from GitHub."""
        logger.info(f"Downloading Azure Security Benchmark file to {file_path}")

        # Azure Security Benchmark v3 URL - updated to the correct GitHub location
        benchmark_url = "https://github.com/MicrosoftDocs/SecurityBenchmarks/blob/master/Azure%20Security%20Benchmark/3.0/azure-security-benchmark-v3.0.xlsx?raw=true"

        try:
            # Download file
            response = requests.get(benchmark_url, stream=True)
            response.raise_for_status()

            # Save to file
            with open(file_path, "wb") as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)

            logger.info(f"Successfully downloaded benchmark file to {file_path}")

        except Exception as e:
            logger.error(f"Error downloading benchmark file: {e}")
            raise

    def _convert_benchmark_to_json(self, excel_path: Path, json_path: Path) -> Dict:
        """
        Convert all worksheets from the Azure Security Benchmark Excel file to a comprehensive RAG-optimized JSON format.

        Creates a structured JSON format specifically optimized for Azure OpenAI RAG patterns,
        with chunks sized appropriately for embeddings and rich semantic relationships.

        IMPORTANT: Also maintains a traditional controls array for use by the security scanner.
        """
        logger.info(
            f"Converting benchmark Excel file to RAG-optimized JSON: {excel_path} -> {json_path}"
        )

        # Check if pandas is available
        if not PANDAS_AVAILABLE:
            logger.warning("pandas not installed. Using simplified benchmark data.")
            return self._get_simplified_benchmark()

        try:
            # Get all sheet names from the Excel file
            excel_file = pd.ExcelFile(excel_path)
            sheet_names = excel_file.sheet_names
            logger.info(
                f"Found {len(sheet_names)} worksheets: {', '.join(sheet_names)}"
            )

            # Initialize the result structure
            result = {
                "metadata": {
                    "version": "3.0",
                    "source": "Microsoft Azure Security Benchmark",
                    "processed_date": datetime.datetime.now().isoformat(),
                    "sheets": sheet_names,
                    "rag_optimized": True,
                    "embedding_model": "text-embedding-ada-002",
                    "chunk_strategy": "semantic_control_based",
                },
                "controls": [],  # This will be populated for the security scanner
                "sheets": {},
                "chunks": [],  # This will be populated for RAG
            }

            # Process each sheet
            main_sheet = None  # Will store the main benchmark sheet

            for sheet_name in sheet_names:
                df = pd.read_excel(excel_path, sheet_name=sheet_name)
                result["sheets"][sheet_name] = df.to_dict("records")

                # Try to identify the main benchmark sheet
                if "security benchmark" in sheet_name.lower():
                    main_sheet = sheet_name
                    logger.info(f"Identified main benchmark sheet: {sheet_name}")

            # If we couldn't identify the main sheet, look for column headers that suggest it's the main sheet
            if not main_sheet:
                for sheet_name in sheet_names:
                    df = pd.read_excel(excel_path, sheet_name=sheet_name)
                    if any(
                        col
                        for col in df.columns
                        if "ASB ID" in str(col) or "Control ID" in str(col)
                    ):
                        main_sheet = sheet_name
                        logger.info(
                            f"Identified main benchmark sheet by columns: {sheet_name}"
                        )
                        break

                if not main_sheet and len(sheet_names) > 1:
                    # If still not found, use the second sheet (first is often a readme)
                    main_sheet = sheet_names[1]
                    logger.info(
                        f"Using default second sheet as main benchmark: {main_sheet}"
                    )

            # Process the main benchmark sheet to extract controls
            if main_sheet:
                df = pd.read_excel(excel_path, sheet_name=main_sheet)

                # Create a mapping of column names to indices
                # We'll use string column names throughout to avoid confusion
                columns = {}
                for i, col in enumerate(df.columns):
                    # Convert column name to string to handle potential numeric column names
                    col_str = str(col)
                    columns[col_str] = i

                # Common column names or patterns to look for
                id_cols = [
                    col
                    for col in columns.keys()
                    if "asb id" in col.lower()
                    or "control id" in col.lower()
                    or col.lower() == "id"
                ]

                # Process rows to extract controls
                for row_idx, row in df.iterrows():
                    # Skip header rows
                    if row_idx < 1:
                        continue

                    # Try to find a control ID in this row
                    control_id = None
                    for id_col in id_cols:
                        if not pd.isna(row[id_col]) and str(row[id_col]).strip():
                            control_id = str(row[id_col]).strip()
                            break

                    # Skip rows without a control ID
                    if not control_id or len(control_id) < 2:
                        continue

                    # Extract control data from this row
                    control = self._extract_control_from_row(
                        row, columns, main_sheet, control_id
                    )

                    if control:
                        # Add to controls array for the security scanner
                        result["controls"].append(control)

                        # Also create RAG chunks for this control
                        self._add_rag_chunks_for_control(control, result["chunks"])

                # Log the number of controls extracted
                logger.info(
                    f"Extracted {len(result['controls'])} controls with {len(result['chunks'])} RAG chunks"
                )
            else:
                logger.warning("Could not identify main benchmark sheet")

            # If we didn't extract any controls, use the simplified benchmark
            if not result["controls"]:
                logger.warning(
                    "No controls extracted from benchmark file, using simplified benchmark"
                )
                return self._get_simplified_benchmark()

            # Save to JSON file
            with open(json_path, "w") as json_file:
                json.dump(result, json_file, indent=2)

            return result

        except Exception as e:
            logger.error(f"Error converting benchmark to JSON: {str(e)}")
            logger.exception(e)
            return self._get_simplified_benchmark()

    def _extract_control_from_row(self, row, columns, sheet_name, control_id):
        """
        Extract control information from a row in the Excel sheet.
        Handles different Excel formats by looking for common naming patterns.

        Args:
            row: The row from the Excel sheet (either pandas Series or namedtuple)
            columns: Dictionary mapping column names to indices or column names
            sheet_name: Name of the sheet
            control_id: The ID of the control

        Returns:
            Dictionary with control information
        """
        # Initialize control structure
        control = {
            "id": control_id,
            "name": "",
            "description": "",
            "sheet": sheet_name,
            "resource_types": [],
            "severity": "HIGH",  # Default to high severity for security
            "related_controls": [],
        }

        try:
            # Handle both Series and namedtuple rows
            is_namedtuple = hasattr(row, "_fields")

            # Try to extract control name
            for col in columns:
                try:
                    # Get value based on type of row and columns
                    if is_namedtuple:
                        # For namedtuples from itertuples, column names are converted to field names
                        # We need to look for the field by index
                        if (
                            col.lower() in ["name", "title", "control name"]
                            and "id" not in col.lower()
                        ):
                            # Access by index - first field is Index, so we add 1
                            col_idx = columns[col] + 1
                            if col_idx < len(row):
                                value = row[col_idx]
                                if value and str(value).strip():
                                    control["name"] = str(value).strip()
                                    break
                    else:
                        # For Series, direct column access
                        if (
                            col.lower() in ["name", "title", "control name"]
                            and "id" not in col.lower()
                        ):
                            value = row.get(col)
                            if value and not pd.isna(value) and str(value).strip():
                                control["name"] = str(value).strip()
                                break
                except Exception as e:
                    logger.debug(f"Error extracting name from column {col}: {str(e)}")

            # Try to extract control description
            for col in columns:
                try:
                    if is_namedtuple:
                        if any(
                            desc_pattern in col.lower()
                            for desc_pattern in [
                                "description",
                                "requirement",
                                "details",
                            ]
                        ):
                            col_idx = columns[col] + 1
                            if col_idx < len(row):
                                value = row[col_idx]
                                if value and str(value).strip():
                                    control["description"] = str(value).strip()
                                    break
                    else:
                        if any(
                            desc_pattern in col.lower()
                            for desc_pattern in [
                                "description",
                                "requirement",
                                "details",
                            ]
                        ):
                            value = row.get(col)
                            if value and not pd.isna(value) and str(value).strip():
                                control["description"] = str(value).strip()
                                break
                except Exception as e:
                    logger.debug(
                        f"Error extracting description from column {col}: {str(e)}"
                    )

            # Try to extract severity/impact
            for col in columns:
                try:
                    if is_namedtuple:
                        if any(
                            sev_pattern in col.lower()
                            for sev_pattern in ["severity", "impact", "priority"]
                        ):
                            col_idx = columns[col] + 1
                            if col_idx < len(row):
                                value = row[col_idx]
                                if value and str(value).strip():
                                    value_lower = str(value).lower().strip()
                                    if (
                                        "high" in value_lower
                                        or "critical" in value_lower
                                    ):
                                        control["severity"] = "HIGH"
                                    elif (
                                        "medium" in value_lower
                                        or "moderate" in value_lower
                                    ):
                                        control["severity"] = "MEDIUM"
                                    elif "low" in value_lower:
                                        control["severity"] = "LOW"
                                    break
                    else:
                        if any(
                            sev_pattern in col.lower()
                            for sev_pattern in ["severity", "impact", "priority"]
                        ):
                            value = row.get(col)
                            if value and not pd.isna(value) and str(value).strip():
                                value_lower = str(value).lower().strip()
                                if "high" in value_lower or "critical" in value_lower:
                                    control["severity"] = "HIGH"
                                elif (
                                    "medium" in value_lower or "moderate" in value_lower
                                ):
                                    control["severity"] = "MEDIUM"
                                elif "low" in value_lower:
                                    control["severity"] = "LOW"
                                break
                except Exception as e:
                    logger.debug(
                        f"Error extracting severity from column {col}: {str(e)}"
                    )

            # Try to identify resource types from the control description and name
            resource_type_keywords = {
                "Storage": ["storage", "blob", "container", "file share"],
                "Key Vault": ["key vault", "keyvault", "secret", "certificate"],
                "Compute": ["vm", "virtual machine", "vmss", "scale set"],
                "Network": [
                    "vnet",
                    "subnet",
                    "nsg",
                    "network security group",
                    "firewall",
                ],
                "SQL": ["sql", "database", "datawarehouse"],
                "App Service": ["app service", "web app", "function app"],
                "Container": ["aks", "kubernetes", "container", "docker", "acr"],
            }

            # Check for resource types in the description
            search_text = (control["name"] + " " + control["description"]).lower()

            for resource_type, keywords in resource_type_keywords.items():
                for keyword in keywords:
                    if keyword.lower() in search_text:
                        if resource_type not in control["resource_types"]:
                            control["resource_types"].append(resource_type)

            # If no resource types found, mark as general
            if not control["resource_types"]:
                control["resource_types"].append("General")

            # Try to extract related controls (e.g., NIST, CIS, etc.)
            for col in columns:
                try:
                    col_name = col.lower()
                    if any(
                        rel_pattern in col_name
                        for rel_pattern in [
                            "related",
                            "mapping",
                            "nist",
                            "cis",
                            "iso",
                            "pci",
                        ]
                    ):
                        if is_namedtuple:
                            col_idx = columns[col] + 1
                            if col_idx < len(row):
                                value = row[col_idx]
                                if value and str(value).strip():
                                    control["related_controls"].append(
                                        {
                                            "framework": col,
                                            "control": str(value).strip(),
                                        }
                                    )
                        else:
                            value = row.get(col)
                            if value and not pd.isna(value) and str(value).strip():
                                control["related_controls"].append(
                                    {"framework": col, "control": str(value).strip()}
                                )
                except Exception as e:
                    logger.debug(
                        f"Error extracting related controls from column {col}: {str(e)}"
                    )

            return control

        except Exception as e:
            logger.error(f"Error extracting control from row: {str(e)}")
            return None

    def _create_searchable_content(self, control):
        """Create optimized searchable content for a control, formatted for effective embedding."""
        parts = []

        # Add primary control information
        parts.append(f"Control ID: {control['id']}")

        if "name" in control:
            parts.append(f"Control Name: {control['name']}")

        if "domain" in control:
            parts.append(f"Security Domain: {control['domain']}")

        if "description" in control:
            parts.append(f"Description: {control['description']}")

        if "severity" in control:
            parts.append(f"Severity: {control['severity']}")

        if "impact" in control:
            parts.append(f"Impact: {control['impact']}")

        if "implementation" in control:
            parts.append(f"Implementation Guidance: {control['implementation']}")

        # Add resource-specific guidance in a structured way
        if "azure_guidance" in control:
            parts.append("Azure Resource-Specific Guidance:")
            for resource, guidance in control["azure_guidance"].items():
                parts.append(f"  - {resource}: {guidance}")

        # Combine with appropriate spacing for embedding
        return "\n".join(parts)

    def _add_rag_chunks_for_control(self, control, chunks_list):
        """Create optimized RAG chunks from a control."""
        # Create a primary chunk for the control overview
        primary_chunk = {
            "id": f"chunk_{control['id']}_overview",
            "control_id": control["id"],
            "chunk_type": "control_overview",
            "content": f"Control ID: {control['id']}\n",
        }

        if "name" in control:
            primary_chunk["content"] += f"Name: {control['name']}\n"

        if "domain" in control:
            primary_chunk["content"] += f"Domain: {control['domain']}\n"

        if "description" in control:
            primary_chunk["content"] += f"Description: {control['description']}\n"

        if "severity" in control:
            primary_chunk["content"] += f"Severity: {control['severity']}\n"

        if "impact" in control:
            primary_chunk["content"] += f"Impact: {control['impact']}\n"

        # Add metadata for better retrieval
        primary_chunk["metadata"] = {
            "control_id": control["id"],
            "severity": control.get("severity", "UNKNOWN"),
            "domain": control.get("domain", "UNKNOWN"),
            "chunk_type": "overview",
        }

        chunks_list.append(primary_chunk)

        # Create a separate chunk for implementation guidance
        if "implementation" in control:
            implementation_chunk = {
                "id": f"chunk_{control['id']}_implementation",
                "control_id": control["id"],
                "chunk_type": "implementation_guidance",
                "content": f"Control ID: {control['id']} - Implementation Guidance\n\n{control['implementation']}",
                "metadata": {
                    "control_id": control["id"],
                    "severity": control.get("severity", "UNKNOWN"),
                    "domain": control.get("domain", "UNKNOWN"),
                    "chunk_type": "implementation",
                },
            }
            chunks_list.append(implementation_chunk)

        # Create resource-specific chunks for better targeting
        if "azure_guidance" in control:
            for resource, guidance in control["azure_guidance"].items():
                resource_chunk = {
                    "id": f"chunk_{control['id']}_{resource.lower().replace(' ', '_')}",
                    "control_id": control["id"],
                    "chunk_type": "resource_guidance",
                    "resource_type": resource,
                    "content": f"Control ID: {control['id']} - {resource} Guidance\n\n{guidance}",
                    "metadata": {
                        "control_id": control["id"],
                        "severity": control.get("severity", "UNKNOWN"),
                        "domain": control.get("domain", "UNKNOWN"),
                        "resource_type": resource,
                        "chunk_type": "resource_guidance",
                    },
                }
                chunks_list.append(resource_chunk)

    def _build_resource_relationships(self, control, resource_types_dict):
        """Build relationships between controls and resource types."""
        if "azure_guidance" in control:
            for resource, guidance in control["azure_guidance"].items():
                if resource not in resource_types_dict:
                    resource_types_dict[resource] = {"name": resource, "controls": []}

                resource_types_dict[resource]["controls"].append(
                    {
                        "control_id": control["id"],
                        "severity": control.get("severity", "UNKNOWN"),
                        "guidance": guidance,
                    }
                )

    def _build_control_relationships(self, controls, relationships_dict):
        """Build semantic relationships between controls."""
        # Group controls by domain
        domains = {}
        for control in controls:
            if "domain" in control:
                domain = control["domain"]
                if domain not in domains:
                    domains[domain] = []
                domains[domain].append(control["id"])

        # Add domain relationships
        for domain, control_ids in domains.items():
            relationships_dict[domain] = {"type": "domain", "controls": control_ids}

        # Group controls by severity
        severities = {}
        for control in controls:
            if "severity" in control:
                severity = control["severity"]
                if severity not in severities:
                    severities[severity] = []
                severities[severity].append(control["id"])

        # Add severity relationships
        for severity, control_ids in severities.items():
            relationships_dict[f"severity_{severity}"] = {
                "type": "severity",
                "severity": severity,
                "controls": control_ids,
            }

        # Group controls with similar prefixes (usually related controls)
        prefixes = {}
        for control in controls:
            prefix = (
                control["id"].split(".")[0] if "." in control["id"] else control["id"]
            )
            if prefix not in prefixes:
                prefixes[prefix] = []
            prefixes[prefix].append(control["id"])

        # Add prefix-based relationships
        for prefix, control_ids in prefixes.items():
            if len(control_ids) > 1:  # Only add if there are multiple controls
                relationships_dict[f"prefix_{prefix}"] = {
                    "type": "related_controls",
                    "prefix": prefix,
                    "controls": control_ids,
                }

    def get_sample_files(self) -> List[Dict]:
        """
        Get sample files for demonstration.
        """
        logger.info("Creating sample files for analysis")

        # Create a sample file with known security issues
        files_info = [
            {
                "path": "sample/storage_account.tf",
                "content": """
            resource "azurerm_storage_account" "example" {
                name                     = "examplestorage"
                resource_group_name      = azurerm_resource_group.example.name
                location                 = azurerm_resource_group.example.location
                account_tier             = "Standard"
                account_replication_type = "LRS"
                
                # Security issue: Allow HTTP traffic
                enable_https_traffic_only = false
                
                # Security issue: No minimum TLS version
                # min_tls_version = "TLS1_2"
                
                # Security issue: No network rules
                network_rules {
                    default_action = "Allow"
                }
            }
            """,
                "resource_type": "Storage",
            }
        ]

        logger.info(f"Created {len(files_info)} sample files for analysis")
        return files_info

    def _determine_resource_type(self, file_path: str, content: str) -> str:
        """
        Determine the Azure resource type from file content using comprehensive mapping.

        Args:
            file_path: Path to the file being analyzed
            content: Content of the file

        Returns:
            str: Detected resource type or "Generic" if no specific type is found
        """
        # Convert content to lowercase for case-insensitive matching
        content_lower = content.lower()
        file_extension = os.path.splitext(file_path)[1].lower()
        file_name_lower = os.path.basename(file_path).lower()

        # Initialize a score dictionary for each resource type
        resource_scores = {
            resource_type: 0 for resource_type in self.AZURE_RESOURCE_MAPPINGS.keys()
        }

        # Special handling for Bicep files - prioritize ARM resource declarations
        if file_extension == ".bicep":
            import re

            # Extract ARM resource type declarations with improved regex
            # This will match both 'Microsoft.X/y@version' and 'Microsoft.X/y' patterns
            resource_declarations = re.findall(r"resource\s+\w+\s+'([^']+)'", content)
            logger.debug(
                f"Found ARM resource declarations in Bicep: {resource_declarations}"
            )

            # Track all resource providers and their types
            resource_providers = {}

            for declaration in resource_declarations:
                # Remove version info after @ if present
                if "@" in declaration:
                    resource_type_only = declaration.split("@")[0]
                else:
                    resource_type_only = declaration

                # Extract provider and resource type
                parts = resource_type_only.split("/")
                provider = parts[0].lower()
                resource_path = "/".join(parts[1:]).lower() if len(parts) > 1 else ""

                if provider not in resource_providers:
                    resource_providers[provider] = set()
                if resource_path:
                    resource_providers[provider].add(resource_path)

                logger.debug(f"Processing ARM resource type: {resource_type_only}")

                # Look for matches in ARM types with weighted scoring
                for rt, mappings in self.AZURE_RESOURCE_MAPPINGS.items():
                    for arm_type in mappings["arm_types"]:
                        arm_type_lower = arm_type.lower()
                        arm_parts = arm_type_lower.split("/")
                        arm_provider = arm_parts[0]
                        arm_resource = (
                            "/".join(arm_parts[1:]) if len(arm_parts) > 1 else ""
                        )

                        # Exact match with full ARM type (highest priority)
                        if resource_type_only.lower() == arm_type_lower:
                            resource_scores[rt] += 10
                            logger.debug(f"Exact ARM type match: {rt} +10 points")
                            continue

                        # Provider and resource type match
                        if (
                            provider == arm_provider
                            and resource_path
                            and arm_resource
                            and resource_path.startswith(arm_resource)
                        ):
                            resource_scores[rt] += 8
                            logger.debug(f"Provider and resource match: {rt} +8 points")
                            continue

                        # Provider match with any declared resource
                        if provider == arm_provider:
                            resource_scores[rt] += 5
                            logger.debug(f"Provider match: {rt} +5 points")
                            continue

            # Apply penalties for mismatched providers
            if resource_providers:
                for rt, mappings in self.AZURE_RESOURCE_MAPPINGS.items():
                    has_matching_provider = False
                    for arm_type in mappings["arm_types"]:
                        provider = arm_type.split("/")[0].lower()
                        if provider in resource_providers:
                            has_matching_provider = True
                            break

                    if not has_matching_provider:
                        resource_scores[rt] -= 5
                        logger.debug(f"Provider mismatch penalty: {rt} -5 points")

        # Standard resource type detection (lower priority than ARM declarations)
        for resource_type, mappings in self.AZURE_RESOURCE_MAPPINGS.items():
            # Check ARM template types for JSON and ARM files
            if file_extension in [".json", ".arm"]:
                for arm_type in mappings["arm_types"]:
                    if arm_type.lower() in content_lower:
                        resource_scores[resource_type] += 3

            # Check Terraform types for TF files
            elif file_extension == ".tf":
                for tf_type in mappings["terraform_types"]:
                    if tf_type.lower() in content_lower:
                        resource_scores[resource_type] += 3

            # Additional Bicep type checking (lower priority)
            elif file_extension == ".bicep":
                for bicep_type in mappings["bicep_types"]:
                    if bicep_type.lower() in content_lower:
                        resource_scores[resource_type] += 1

            # Check keywords (lowest priority)
            for keyword in mappings["keywords"]:
                if keyword.lower() in content_lower:
                    resource_scores[resource_type] += 0.5

        # Log scores for debugging
        logger.debug(f"Resource type scores for {file_path}: {resource_scores}")

        # Get the resource type with the highest score
        max_score = max(resource_scores.values())
        if max_score > 0:
            # Get all types with the highest score
            max_types = [
                rt for rt, score in resource_scores.items() if score == max_score
            ]

            if len(max_types) == 1:
                logger.info(
                    f"Detected resource type for {file_path}: {max_types[0]} (score: {max_score})"
                )
                return max_types[0]
            else:
                # In case of a tie, use file name hints to break it
                logger.debug(f"Tie between resource types: {max_types}")
                for rt in max_types:
                    if rt.lower() in file_name_lower:
                        logger.info(f"Tie broken by filename, detected type: {rt}")
                        return rt
                # If still tied, return Generic to avoid incorrect categorization
                logger.info(f"Tie between {max_types}, defaulting to Generic")
                return "Generic"

        # If no match found
        logger.info(
            f"No specific resource type detected for {file_path}, returning Generic"
        )
        return "Generic"

    def analyze_folder(self, folder_path: str) -> List[Dict]:
        """Analyze local folder by recursively collecting supported files and analyzing them."""
        supported_exts = {".tf", ".bicep", ".json", ".yaml", ".yml"}
        files = []
        for root, _, filenames in os.walk(folder_path):
            for fname in filenames:
                ext = os.path.splitext(fname)[1].lower()
                if ext in supported_exts:
                    fpath = os.path.join(root, fname)
                    with open(fpath, "r", encoding="utf-8") as f:
                        content = f.read()
                    rel_path = os.path.relpath(fpath, folder_path)
                    files.append({"path": rel_path, "content": content})
        return self.analyze_files(files)

    def analyze_files(self, files: List[Dict]) -> List[Dict]:
        """
        Analyze files for security issues using Azure OpenAI and the RAG-optimized benchmark.

        This method scans each file for security issues by:
        1. First applying pattern-based detections for common issues
        2. Then using Azure OpenAI with RAG for more sophisticated analysis
        """
        if not self.benchmark_data:
            logger.warning(
                "Benchmark data not initialized. Using default security rules."
            )
            # Initialize a simple default benchmark if missing
            self.benchmark_data = self._get_simplified_benchmark()
            # self.benchmark_data = self._convert_benchmark_to_json()

        findings = []

        for file_info in files:
            # Determine resource type if not already set
            if not "resource_type" in file_info:
                resource_type = self._determine_resource_type(
                    file_info["path"], file_info["content"]
                )
                file_info["resource_type"] = resource_type
            else:
                resource_type = file_info["resource_type"]

            logger.info(
                f"Analyzing file {file_info['path']} for {resource_type} security issues"
            )

            # Apply pattern-based detections first (quick and reliable)
            pattern_findings = self._detect_common_issues(file_info)
            if pattern_findings:
                findings.extend(pattern_findings)
                logger.info(
                    f"Found {len(pattern_findings)} pattern-based security issues in {file_info['path']}"
                )

            # Get relevant benchmark controls for this resource type
            resource_keywords = []
            if resource_type == "Storage":
                resource_keywords = ["storage", "blob", "container", "file share"]
            elif resource_type == "KeyVault":
                resource_keywords = ["key vault", "keyvault", "secret", "certificate"]
            elif resource_type == "VirtualNetwork":
                resource_keywords = ["vnet", "network", "nsg", "firewall", "subnet"]
            elif resource_type == "Compute":
                resource_keywords = ["vm", "virtual machine", "compute"]
            else:
                resource_keywords = ["security", "generic", "general"]

            # Log the keywords we're searching for
            logger.info(
                f"Searching for controls related to: {', '.join(resource_keywords)}"
            )

            # Get relevant benchmark controls for this resource type using more sophisticated matching
            relevant_controls = self._find_relevant_controls(resource_type)

            if not relevant_controls:
                logger.warning(
                    f"No relevant controls found for resource type: {resource_type}"
                )
                continue

            # Prepare context for Azure OpenAI
            context = self._prepare_analysis_context(file_info, relevant_controls)

            # Call Azure OpenAI for analysis
            try:
                logger.info(f"Analyzing {file_info['path']} with Azure OpenAI")

                # Get deployment name
                deployment = os.environ.get("AZURE_OPENAI_DEPLOYMENT", "gpt-35-turbo")
                if not deployment:
                    logger.warning(
                        "AZURE_OPENAI_DEPLOYMENT not set, using default: gpt-35-turbo"
                    )
                    deployment = "gpt-35-turbo"

                logger.info(f"Using Azure OpenAI deployment: {deployment}")

                # Call Azure OpenAI using the latest SDK approach
                try:
                    # Get model-appropriate parameters
                    api_params = get_openai_api_params(
                        deployment_name=deployment,
                        max_tokens=2000,
                        temperature=0.0,
                        messages=[
                            {
                                "role": "system",
                                "content": "You are a security expert specializing in Azure security. Your task is to identify security issues in Azure resources based on the Azure Security Benchmark v3.0.",
                            },
                            {"role": "user", "content": context},
                        ]
                    )

                    response = self.openai_client.chat.completions.create(**api_params)

                    # Extract the content from the response
                    result_text = response.choices[0].message.content
                except Exception as e:
                    # Fall back to the older API pattern if the new pattern fails
                    logger.warning(
                        f"Error using new SDK pattern: {str(e)}, falling back to older pattern"
                    )

                    # Set up API params for the classic OpenAI call pattern
                    openai.api_type = "azure"
                    openai.api_base = os.environ.get("AZURE_OPENAI_ENDPOINT", "")
                    openai.api_key = os.environ.get("AZURE_OPENAI_API_KEY", "")
                    openai.api_version = os.environ.get(
                        "AZURE_OPENAI_API_VERSION", "2023-05-15"
                    )
                    deployment = os.environ.get(
                        "AZURE_OPENAI_DEPLOYMENT", "gpt-35-turbo"
                    )

                    # Make the call using the classic pattern with model-appropriate parameters
                    api_params = get_openai_api_params(
                        deployment_name=deployment,
                        max_tokens=2000,
                        temperature=0.0,
                        messages=[
                            {
                                "role": "system",
                                "content": "You are a security expert specializing in Azure security. Your task is to identify security issues in Azure resources based on the Azure Security Benchmark v3.0.",
                            },
                            {"role": "user", "content": context},
                        ]
                    )

                    response = self.openai_client.chat.completions.create(**api_params)

                    # Extract the content from the response
                    result_text = response.choices[0].message.content
                    print(f"Response: {result_text}")

                # Parse the response content
                if "```json" in result_text:
                    # Extract JSON part
                    json_start = result_text.find("```json") + 7
                    json_end = result_text.find("```", json_start)
                    json_content = result_text[json_start:json_end].strip()

                    try:
                        ai_findings = json.loads(json_content)
                        if isinstance(ai_findings, list):
                            for finding in ai_findings:
                                finding["file_path"] = file_info["path"]
                                findings.append(finding)
                        else:
                            logger.warning(
                                f"Unexpected AI response format: {json_content}"
                            )
                    except json.JSONDecodeError:
                        logger.warning(
                            f"Failed to parse JSON from OpenAI response: {json_content}"
                        )
                else:
                    # Try to extract findings with basic parsing
                    import re

                    issues = re.split(r"Issue \d+:", result_text)
                    if (
                        len(issues) > 1
                    ):  # Skip the first split which is text before "Issue 1"
                        for i, issue_text in enumerate(issues[1:]):
                            # Try to extract severity and line numbers
                            severity_match = re.search(r"Severity:\s*(\w+)", issue_text)
                            line_match = re.search(r"Line:\s*(\d+)", issue_text)
                            description_match = re.search(
                                r"Description:\s*(.+?)(?=\n\n|\n[A-Z]|$)",
                                issue_text,
                                re.DOTALL,
                            )

                            severity = (
                                severity_match.group(1) if severity_match else "MEDIUM"
                            )
                            line = int(line_match.group(1)) if line_match else 1
                            description = (
                                description_match.group(1).strip()
                                if description_match
                                else issue_text.strip()
                            )

                            findings.append(
                                {
                                    "file_path": file_info["path"],
                                    "severity": severity.upper(),
                                    "line": line,
                                    "description": description,
                                    "control_id": f"AUTO-{i+1}",
                                    "remediation": "Review the code and apply security best practices.",
                                }
                            )

            except Exception as e:
                logger.error(f"Error analyzing {file_info['path']}: {str(e)}")
                logger.exception(e)

        if not findings:
            # If no findings from OpenAI, apply default pattern-based detections
            for file_info in files:
                pattern_findings = self._detect_common_issues(file_info)
                if pattern_findings:
                    findings.extend(pattern_findings)

        logger.info(
            f"Found {len(findings)} total security issues in {len(files)} files"
        )
        return findings

    def _detect_common_issues(self, file_info: Dict) -> List[Dict]:
        """
        Detect common security issues using pattern matching.
        This is more reliable for known security patterns than AI and faster.
        TODO: Need to add to do recommendations for each file using AI and then compare with patterns and add to findings
        """
        findings = []
        # Keep original content intact for accurate line numbers
        file_content = file_info["content"]
        file_content_lower = file_info[
            "content"
        ].lower()  # For case-insensitive searching if needed
        path = file_info["path"]
        resource_type = file_info.get("resource_type", "Generic")

        # Normalize line endings to ensure consistent line number calculation
        file_content = file_content.replace("\r\n", "\n")

        # Define patterns and their corresponding security issues
        patterns = []

        # Define patterns with flags for case-insensitive matching
        patterns = []

        # Storage Account Security Issues
        if resource_type == "Storage":
            patterns.extend(
                [
                    {
                        "pattern": re.compile(
                            r"enable_https_traffic_only\s*=\s*false", re.IGNORECASE
                        ),
                        "severity": "HIGH",
                        "control_id": "DP-3",
                        "description": "Storage account allows HTTP traffic, which is insecure. HTTPS should be enforced.",
                        "remediation": "Set enable_https_traffic_only = true to enforce HTTPS.",
                    },
                    {
                        "pattern": re.compile(
                            r"min_tls_version\s*=\s*[\"']TLS1_0[\"']", re.IGNORECASE
                        ),
                        "severity": "MEDIUM",
                        "control_id": "DP-4",
                        "description": "Storage account uses an outdated TLS version which has known vulnerabilities.",
                        "remediation": 'Set min_tls_version = "TLS1_2" to enforce stronger encryption.',
                    },
                    {
                        "pattern": re.compile(
                            r"network_rules\s*{[^}]*default_action\s*=\s*[\"']allow[\"']",
                            re.IGNORECASE,
                        ),
                        "severity": "HIGH",
                        "control_id": "NS-1",
                        "description": "Storage account allows access from all networks. This creates unnecessary security exposure.",
                        "remediation": 'Set default_action = "Deny" and explicitly allow only necessary networks.',
                    },
                    {
                        "pattern": re.compile(
                            r"public_network_access_enabled\s*=\s*true", re.IGNORECASE
                        ),
                        "severity": "MEDIUM",
                        "control_id": "NS-2",
                        "description": "Storage account allows public network access, increasing attack surface.",
                        "remediation": "Consider setting public_network_access_enabled = false and use private endpoints.",
                    },
                ]
            )

        # For Bicep files, check for similar patterns with different syntax
        if path.endswith(".bicep"):
            patterns.extend(
                [
                    {
                        "pattern": re.compile(
                            r"enableHttpsTrafficOnly:\s*false", re.IGNORECASE
                        ),
                        "severity": "HIGH",
                        "control_id": "DP-3",
                        "description": "Storage account allows HTTP traffic, which is insecure. HTTPS should be enforced.",
                        "remediation": "Set enableHttpsTrafficOnly: true to enforce HTTPS.",
                    },
                    {
                        "pattern": re.compile(
                            r"minimumTlsVersion:\s*[\"']TLS1_0[\"']", re.IGNORECASE
                        ),
                        "severity": "MEDIUM",
                        "control_id": "DP-4",
                        "description": "Storage account uses an outdated TLS version which has known vulnerabilities.",
                        "remediation": "Set minimumTlsVersion: 'TLS1_2' to enforce stronger encryption.",
                    },
                    {
                        "pattern": re.compile(
                            r"networkAcls:[^}]*defaultAction:\s*[\"']Allow[\"']",
                            re.IGNORECASE,
                        ),
                        "severity": "HIGH",
                        "control_id": "NS-1",
                        "description": "Storage account allows access from all networks. This creates unnecessary security exposure.",
                        "remediation": "Set defaultAction: 'Deny' and explicitly allow only necessary networks.",
                    },
                    {
                        "pattern": re.compile(
                            r"publicNetworkAccess:\s*[\"']Enabled[\"']", re.IGNORECASE
                        ),
                        "severity": "MEDIUM",
                        "control_id": "NS-2",
                        "description": "Storage account allows public network access, increasing attack surface.",
                        "remediation": "Consider setting publicNetworkAccess: 'Disabled' and use private endpoints.",
                    },
                ]
            )

        # Check each pattern against the file content
        for pattern_info in patterns:
            pattern = pattern_info["pattern"]  # Now a compiled regex pattern with flags

            matches = list(pattern.finditer(file_content))
            for match in matches:
                # Get accurate line number from the original content
                line_number = file_content[: match.start()].count("\n") + 1

                # Extract the actual line content for validation
                lines = file_content.split("\n")
                if 0 <= line_number - 1 < len(lines):
                    matching_line = lines[line_number - 1].strip()

                    findings.append(
                        {
                            "file_path": path,
                            "line": line_number,
                            "matching_content": matching_line,  # Add the actual content for verification
                            "severity": pattern_info["severity"],
                            "control_id": pattern_info["control_id"],
                            "description": pattern_info["description"],
                            "remediation": pattern_info["remediation"],
                        }
                    )

                    # Log the match for debugging
                    logger.debug(
                        f"Found security issue in {path} at line {line_number}: {matching_line}"
                    )

        return findings

    def _find_relevant_controls(self, resource_type: str) -> List[Dict]:
        """
        Find controls in the RAG-optimized benchmark that are relevant to the given resource type.
        Uses the comprehensive AZURE_RESOURCE_MAPPINGS for better matching.
        """
        if not self.benchmark_data or "controls" not in self.benchmark_data:
            return []

        relevant_controls = []
        resource_type_lower = resource_type.lower()

        # Get keywords from the comprehensive mapping
        relevant_keywords = [resource_type_lower]

        # Find the resource type in our comprehensive mapping
        for mapped_type, mappings in self.AZURE_RESOURCE_MAPPINGS.items():
            if mapped_type.lower() == resource_type_lower:
                # Add all keywords from the comprehensive mapping
                relevant_keywords.extend(
                    [kw.lower() for kw in mappings.get("keywords", [])]
                )

                # Also add simplified versions of ARM, Terraform, and Bicep types
                for arm_type in mappings.get("arm_types", []):
                    # Extract the resource name from ARM type (e.g., "Microsoft.Storage/storageAccounts" -> "storageAccounts")
                    parts = arm_type.split("/")
                    if len(parts) > 1:
                        relevant_keywords.append(parts[-1].lower())

                for tf_type in mappings.get("terraform_types", []):
                    # Extract resource name from Terraform type (e.g., "azurerm_storage_account" -> "storage_account")
                    if tf_type.startswith("azurerm_"):
                        relevant_keywords.append(
                            tf_type[7:].lower()
                        )  # Remove 'azurerm_' prefix

                for bicep_type in mappings.get("bicep_types", []):
                    relevant_keywords.append(bicep_type.lower())
                break

        # If resource type not found in mapping, try to find it by checking if it's mentioned in any keywords
        if len(relevant_keywords) == 1:  # Only has the resource_type itself
            for mapped_type, mappings in self.AZURE_RESOURCE_MAPPINGS.items():
                keywords = [kw.lower() for kw in mappings.get("keywords", [])]
                if any(
                    resource_type_lower in kw or kw in resource_type_lower
                    for kw in keywords
                ):
                    relevant_keywords.extend(keywords)
                    # Also add the mapped type name
                    relevant_keywords.append(mapped_type.lower())
                    break

        # Remove duplicates while preserving order
        seen = set()
        unique_keywords = []
        for kw in relevant_keywords:
            if kw not in seen:
                seen.add(kw)
                unique_keywords.append(kw)
        relevant_keywords = unique_keywords

        logger.info(
            f"Searching for controls related to: {', '.join(relevant_keywords)}"
        )

        # Find controls with matching resource types in their descriptions or azure_guidance
        for control in self.benchmark_data["controls"]:
            # Check if control has resource-specific guidance
            if "azure_guidance" in control:
                for resource_key in control["azure_guidance"].keys():
                    if any(kw in resource_key.lower() for kw in relevant_keywords):
                        relevant_controls.append(control)
                        break

            # Check if resource type is mentioned in description
            elif "description" in control:
                control_desc_lower = control["description"].lower()
                if any(kw in control_desc_lower for kw in relevant_keywords):
                    relevant_controls.append(control)

            # Check if resource type is mentioned in control name
            elif "name" in control:
                control_name_lower = control["name"].lower()
                if any(kw in control_name_lower for kw in relevant_keywords):
                    relevant_controls.append(control)

            # Check searchable content if available (especially good for RAG)
            elif "searchable_content" in control:
                searchable_lower = control["searchable_content"].lower()
                if any(kw in searchable_lower for kw in relevant_keywords):
                    relevant_controls.append(control)

            # Check resource_types array if present
            elif "resource_types" in control:
                control_resource_types = [
                    rt.lower() for rt in control.get("resource_types", [])
                ]
                if any(rt in control_resource_types for rt in relevant_keywords):
                    relevant_controls.append(control)

        # Always include critical security controls even if not directly resource-related
        critical_control_ids = [
            "NS-1",
            "NS-2",
            "NS-3",
            "DP-1",
            "DP-2",
            "DP-3",
            "IM-1",
            "IM-2",
            "IM-3",
        ]
        for control in self.benchmark_data["controls"]:
            if (
                "id" in control
                and control["id"] in critical_control_ids
                and control not in relevant_controls
            ):
                relevant_controls.append(control)

        # If we found very few controls, expand the search
        if len(relevant_controls) < 3:
            logger.info(
                f"Found only {len(relevant_controls)} controls, expanding search to general controls"
            )

            # Add general security controls that apply to all resources
            general_keywords = [
                "security",
                "encryption",
                "network",
                "access",
                "authentication",
                "monitoring",
                "logging",
            ]
            for control in self.benchmark_data["controls"]:
                if control not in relevant_controls:
                    control_text = f"{control.get('name', '')} {control.get('description', '')}".lower()
                    if any(kw in control_text for kw in general_keywords):
                        relevant_controls.append(control)
                        if len(relevant_controls) >= 10:  # Limit to reasonable number
                            break

        logger.info(
            f"Found {len(relevant_controls)} relevant controls for {resource_type}"
        )
        return relevant_controls

    # def _find_relevant_controls(self, resource_type: str) -> List[Dict]:

    #     """
    #     Find controls in the RAG-optimized benchmark that are relevant to the given resource type.
    #     Uses a more sophisticated approach to match controls to resource types.
    #     """
    #     if not self.benchmark_data or "controls" not in self.benchmark_data:
    #         return []

    #     relevant_controls = []
    #     resource_type_lower = resource_type.lower()
    #     azure_resource_mappings = {
    #         "storage": ["storage", "blob", "container", "file share"],
    #         "keyvault": ["key vault", "keyvault", "secret", "certificate"],
    #         "compute": ["vm", "virtual machine", "vmss", "scale set"],
    #         "network": ["vnet", "subnet", "nsg", "network security group", "firewall"],
    #         "sql": ["sql", "database", "datawarehouse"],
    #         "webapp": ["app service", "web app", "function app"],
    #         "container": ["aks", "kubernetes", "container", "docker", "acr"]
    #     }

    #     # Find relevant resource type keywords
    #     relevant_keywords = [resource_type_lower]
    #     for category, keywords in azure_resource_mappings.items():
    #         if any(kw in resource_type_lower for kw in keywords):
    #             relevant_keywords.extend(keywords)

    #     logger.info(f"Searching for controls related to: {', '.join(relevant_keywords)}")

    #     # Find controls with matching resource types in their descriptions or azure_guidance
    #     for control in self.benchmark_data["controls"]:
    #         # Check if control has resource-specific guidance
    #         if "azure_guidance" in control:
    #             for resource_key in control["azure_guidance"].keys():
    #                 if any(kw in resource_key.lower() for kw in relevant_keywords):
    #                     relevant_controls.append(control)
    #                     break

    #         # Check if resource type is mentioned in description
    #         elif "description" in control and any(kw in control["description"].lower() for kw in relevant_keywords):
    #             relevant_controls.append(control)

    #         # Check searchable content if available (especially good for RAG)
    #         elif "searchable_content" in control and any(kw in control["searchable_content"].lower() for kw in relevant_keywords):
    #             relevant_controls.append(control)

    #     # Always include critical security controls even if not directly resource-related
    #     critical_control_ids = ["NS-1", "NS-2", "DP-1", "DP-2", "DP-3", "IAM-1", "IAM-2"]
    #     for control in self.benchmark_data["controls"]:
    #         if "id" in control and control["id"] in critical_control_ids and control not in relevant_controls:
    #             relevant_controls.append(control)

    #     logger.info(f"Found {len(relevant_controls)} relevant controls for {resource_type}")
    #     return relevant_controls

    def _prepare_analysis_context(self, file_info: Dict, controls: List[Dict]) -> str:
        """
        Prepare analysis context for Azure OpenAI RAG with file content and relevant controls.
        """
        # Extract file information
        file_path = file_info["path"]
        file_content = file_info["content"]
        resource_type = file_info.get("resource_type", "Generic")

        # Build prompt
        context = f"""## Security Analysis Task

I need you to analyze the following Azure {resource_type} configuration file for security issues.

### File Information
- Path: {file_path}
- Resource Type: {resource_type}

### File Content
```
{file_content}
```

### Relevant Azure Security Benchmark Controls

Based on the Azure Security Benchmark v3.0, the following security controls are relevant to {resource_type} resources:

"""
        # Add benchmark controls to context
        for control in controls:
            # Extract key control info - handle missing fields gracefully
            control_id = control.get("id", "Unknown")
            name = control.get("name", "Unnamed Control")
            description = control.get("description", "No description available")

            # Some controls might have a domain field, but it's optional
            domain = control.get("domain", "Security")

            # Add control details to prompt
            context += f"""
#### {control_id}: {name} 
- Domain: {domain}
- Description: {description}
"""

        # Add analysis instructions
        context += """
### Analysis Instructions

1. Analyze the file for security issues related to the Azure Security Benchmark controls above.
2. For each issue found, provide:
   - Reference to the specific control ID violated
   - Severity (HIGH, MEDIUM, LOW)
   - Line number where the issue occurs
   - Description of the security issue
   - Recommended remediation

3. Format your response as a JSON array of findings:
```json
[
  {
    "control_id": "NS-1",
    "severity": "HIGH",
    "line": 15,
    "description": "Network Security Group allows unrestricted access from the internet",
    "remediation": "Restrict inbound traffic to necessary sources only"
  }
]
```

If no security issues are found, return an empty array [].
"""
        return context

    def post_pr_comments(self, findings: List[Dict]) -> None:
        """Post security findings as comments on the PR."""
        if not findings:
            logger.info("No security issues found to comment on")
            return

        try:
            # Get environment variables
            project = os.environ.get("AZURE_DEVOPS_PROJECT")
            if not project:
                raise ValueError(
                    "Environment variable AZURE_DEVOPS_PROJECT must be set"
                )

            # Get Azure DevOps client
            client = self._init_ado_client()

            # Get the Git client - try both v7.0 and v6.0
            git_client = None
            try:
                # Try v7.0 first
                git_client = client.get_client(
                    "azure.devops.v7_0.git.git_client.GitClient"
                )
            except ModuleNotFoundError:
                try:
                    # Try v6.0 next
                    git_client = client.get_client(
                        "azure.devops.v6_0.git.git_client.GitClient"
                    )
                except ModuleNotFoundError:
                    # Try the default version
                    git_client = client.get_git_client()

            if not git_client:
                logger.error("Failed to get Git client from Azure DevOps SDK")
                return

            logger.info(
                f"Successfully initialized Azure DevOps Git client: {type(git_client).__name__}"
            )

            # Group findings by file
            findings_by_file = {}
            for finding in findings:
                # Make sure the finding has required fields for posting a comment
                file_path = finding.get("file_path", "unknown")
                if "line" not in finding and "line_numbers" not in finding:
                    finding["line"] = 1

                # Add to the proper group
                if file_path not in findings_by_file:
                    findings_by_file[file_path] = []
                findings_by_file[file_path].append(finding)

            # Post a comment for each file with findings
            for file_path, file_findings in findings_by_file.items():
                try:
                    comment = self._format_file_comment(file_path, file_findings)

                    # Create a thread using the available API
                    # Different versions of the SDK have different interfaces
                    try:
                        # Try dictionary-based
                        thread = {
                            "comments": [
                                {
                                    "content": comment,
                                    "parentCommentId": 0,
                                    "commentType": "text",
                                }
                            ],
                            "status": "active",
                            "threadContext": {"filePath": file_path},
                            "pullRequestThreadContext": {"changeTrackingId": 0},
                        }

                        # Post the thread
                        git_client.create_thread(
                            comment_thread=thread,
                            repository_id=self.repo_id,
                            pull_request_id=self.pr_id,
                            project=project,
                        )
                    except Exception as e:
                        logger.warning(
                            f"Failed to create thread with dictionary approach: {str(e)}"
                        )

                        # Try using object based approach with the SDK classes
                        from azure.devops.v6_0.git.models import (
                            Comment,
                            GitPullRequestCommentThread,
                        )

                        comment_obj = Comment(content=comment, comment_type=1)
                        thread = GitPullRequestCommentThread(comments=[comment_obj])

                        git_client.create_thread(
                            comment_thread=thread,
                            repository_id=self.repo_id,
                            pull_request_id=self.pr_id,
                            project=project,
                        )

                    logger.info(f"Posted comment for file: {file_path}")
                except Exception as e:
                    logger.error(
                        f"Error posting comment for file {file_path}: {str(e)}"
                    )

            # Post summary comment
            try:
                comment_content = self._format_summary_comment(findings)

                try:
                    # Try dictionary-based
                    thread = {
                        "comments": [
                            {
                                "content": comment_content,
                                "parentCommentId": 0,
                                "commentType": "text",
                            }
                        ],
                        "status": "active",
                    }

                    # Post the thread
                    git_client.create_thread(
                        comment_thread=thread,
                        repository_id=self.repo_id,
                        pull_request_id=self.pr_id,
                        project=project,
                    )
                except Exception as e:
                    logger.warning(
                        f"Failed to create summary thread with dictionary approach: {str(e)}"
                    )

                    # Try using object based approach
                    from azure.devops.v6_0.git.models import (
                        Comment,
                        GitPullRequestCommentThread,
                    )

                    comment_obj = Comment(content=comment_content, comment_type=1)
                    thread = GitPullRequestCommentThread(comments=[comment_obj])

                    git_client.create_thread(
                        comment_thread=thread,
                        repository_id=self.repo_id,
                        pull_request_id=self.pr_id,
                        project=project,
                    )

                logger.info("Posted security review summary comment")
            except Exception as e:
                logger.error(f"Error posting summary comment: {str(e)}")

            logger.info(
                f"Posted comments for {len(findings)} security issues across {len(findings_by_file)} files"
            )

        except Exception as e:
            logger.error(f"Error posting PR comments: {str(e)}")
            logger.exception(e)

    def _format_file_comment(self, file_path: str, findings: List[Dict]) -> str:
        """Format a comment for a file with security findings."""
        # Sort findings by severity (CRITICAL, HIGH, MEDIUM, LOW)
        severity_order = {"CRITICAL": 0, "HIGH": 1, "MEDIUM": 2, "LOW": 3}
        findings.sort(key=lambda x: severity_order.get(x.get("severity", "LOW"), 4))

        comment = f"## 🔒 Security AI Review: {file_path}\n\n"
        comment += "The following security issues were identified:\n\n"

        # Group by severity for better readability
        current_severity = None

        for finding in findings:
            severity = finding.get("severity", "LOW")
            if severity != current_severity:
                current_severity = severity
                emoji = SEVERITY_EMOJIS.get(severity, "❓")
                comment += f"\n### {emoji} {severity} Severity Issues\n\n"

            # Extract finding details
            control_id = finding.get("control_id", "UNKNOWN")
            description = finding.get("description", "No description provided")
            remediation = finding.get("remediation", "No remediation provided")

            # Format the line numbers
            line_info = "Line: 1"  # Default
            if "line_numbers" in finding and finding["line_numbers"]:
                line_str = ", ".join(str(line) for line in finding["line_numbers"])
                line_info = f"Line(s): {line_str}"
            elif "line" in finding:
                line_info = f"Line: {finding['line']}"

            # Add the actual code content if available
            if "matching_content" in finding:
                line_info += f"\nCode: `{finding['matching_content']}`"

            # Add the finding to the comment
            comment += f"**{control_id}** - {line_info}\n"
            comment += f"{description}\n\n"
            comment += f"**Recommendation:** {remediation}\n\n"
            comment += "---\n\n"

        # Add AI disclaimer
        comment += "\n**CAUTION:** These recommendations are AI-generated and should be reviewed by security professionals before implementation."

        return comment

    def _format_summary_comment(self, findings: List[Dict]) -> str:
        """Format a summary comment for all findings."""
        # Group findings by severity
        findings_by_severity = {"CRITICAL": [], "HIGH": [], "MEDIUM": [], "LOW": []}

        # Categorize findings by severity
        for finding in findings:
            severity = finding.get("severity", "LOW").upper()
            if severity not in findings_by_severity:
                findings_by_severity[severity] = []
            findings_by_severity[severity].append(finding)

        # Create summary content
        comment = "# 🔒 Security AI Review Summary\n\n"

        # Add overall statistics
        comment += "## Overview\n\n"
        total_issues = len(findings)
        comment += f"**Total Security Issues Found: {total_issues}**\n\n"

        # Count by severity in specific order
        for severity in ["CRITICAL", "HIGH", "MEDIUM", "LOW"]:
            items = findings_by_severity.get(severity, [])
            if items:
                emoji = SEVERITY_EMOJIS.get(severity, "❓")
                comment += f"* {emoji} **{severity}**: {len(items)} issues\n"

        # Add file breakdown
        comment += "\n## Files with Security Issues\n\n"

        # Group by file path
        files_with_issues = {}
        for finding in findings:
            file_path = finding.get("file_path", "Unknown")
            if file_path not in files_with_issues:
                files_with_issues[file_path] = []
            files_with_issues[file_path].append(finding)

        # List files with counts
        for file_path, file_findings in files_with_issues.items():
            comment += f"* **{file_path}**: {len(file_findings)} issues\n"

        # Add AI disclaimer
        comment += "\n## AI Disclaimer\n\n"
        comment += "**CAUTION:** These recommendations are AI-generated and should be reviewed by security professionals before implementation. AI outputs may contain errors or miss context-specific requirements.\n\n"

        # Add helpful resources if available
        comment += "\n## Next Steps\n\n"
        comment += "Review the detailed comments on each file for specific remediation steps.\n\n"
        comment += "For more information, refer to:\n"
        comment += "* [Azure Security Benchmark v3](https://learn.microsoft.com/en-us/security/benchmark/azure/overview)\n"
        comment += "* [Azure Security Center](https://azure.microsoft.com/en-us/services/security-center/)\n"

        return comment

    def get_file_content(
        self,
        repository_id: str,
        file_path: str,
        commit_id: str = None,
        branch_name: str = None,
    ) -> str:
        """Get the content of a file from the repository.

        Args:
            repository_id: The ID of the repository
            file_path: The path of the file to get
            commit_id: Optional commit ID to get the file from a specific commit
            branch_name: Optional branch name to get the file from a specific branch

        Returns:
            The content of the file as a string
        """
        try:
            # Get project name from environment
            project = os.environ.get("AZURE_DEVOPS_PROJECT")
            if not project:
                raise ValueError(
                    "Environment variable AZURE_DEVOPS_PROJECT must be set"
                )

            # Get PAT from environment (needed for direct REST API calls)
            pat = os.environ.get("AZURE_DEVOPS_PAT")
            if not pat:
                raise ValueError("Environment variable AZURE_DEVOPS_PAT must be set")

            # Get organization from environment
            org = os.environ.get("AZURE_DEVOPS_ORG")
            if not org:
                raise ValueError("Environment variable AZURE_DEVOPS_ORG must be set")

            # Setup for REST API calls
            base_url = f"https://dev.azure.com/{org}/{project}"

            # Remove leading slash from file_path if present
            if file_path.startswith("/"):
                file_path = file_path[1:]

            # Clean branch name - remove refs/heads/ prefix if it exists
            if branch_name and branch_name.startswith("refs/heads/"):
                branch_name_clean = branch_name.replace("refs/heads/", "")
            else:
                branch_name_clean = branch_name

            logger.info(
                f"Getting file content for {file_path} from {commit_id or branch_name_clean or 'default branch'}"
            )

            # Setup authentication - using HTTP Basic Auth with empty username and PAT as password
            credentials = f":{pat}"
            encoded_credentials = base64.b64encode(credentials.encode("utf-8")).decode(
                "utf-8"
            )
            headers = {"Authorization": f"Basic {encoded_credentials}"}

            # Construct API URL
            api_url = f"{base_url}/_apis/git/repositories/{repository_id}/items"

            # Setup parameters
            params = {
                "api-version": "6.0",
                "path": file_path,
            }

            # If commit ID is provided, use that as the version
            if commit_id:
                params["versionDescriptor.versionType"] = "commit"
                params["versionDescriptor.version"] = commit_id
            # Otherwise if branch name is provided, use that
            elif branch_name:
                params["versionDescriptor.versionType"] = "branch"
                params["versionDescriptor.version"] = branch_name_clean

            # Log the complete URL and parameters for debugging
            url_with_params = f"{api_url}?{urllib.parse.urlencode(params)}"
            logger.info(f"Request URL: {url_with_params}")

            # Make the request to get file content
            response = requests.get(api_url, headers=headers, params=params)
            response.raise_for_status()

            logger.info(
                f"Successfully retrieved content for {file_path} ({len(response.text)} bytes)"
            )
            return response.text

        except requests.exceptions.RequestException as e:
            logger.error(f"Error getting file content for {file_path}: {str(e)}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error getting file content: {str(e)}")
            return None

    def get_pr_files(self) -> List[Dict]:
        """Get the files from a PR."""
        logger.info(f"Getting files from PR {self.pr_id} in repository {self.repo_id}")
        files = []

        try:
            # Get project name from environment
            project = os.environ.get("AZURE_DEVOPS_PROJECT")
            if not project:
                raise ValueError(
                    "Environment variable AZURE_DEVOPS_PROJECT must be set"
                )

            # Get PAT from environment (needed for direct REST API calls)
            pat = os.environ.get("AZURE_DEVOPS_PAT")
            if not pat:
                raise ValueError("Environment variable AZURE_DEVOPS_PAT must be set")

            # Get organization from environment
            org = os.environ.get("AZURE_DEVOPS_ORG")
            if not org:
                raise ValueError("Environment variable AZURE_DEVOPS_ORG must be set")

            # Setup for REST API calls
            base_url = f"https://dev.azure.com/{org}/{project}"
            credentials = f":{pat}"
            encoded_credentials = base64.b64encode(credentials.encode("utf-8")).decode(
                "utf-8"
            )
            headers = {"Authorization": f"Basic {encoded_credentials}"}

            logger.info(f"Using REST API to get PR files from {base_url}")

            # First, get the PR details to get source branch
            pr_url = f"{base_url}/_apis/git/repositories/{self.repo_id}/pullrequests/{self.pr_id}"
            logger.info(f"Getting PR details from {pr_url}")

            pr_response = requests.get(
                pr_url, headers=headers, params={"api-version": "6.0"}
            )
            pr_response.raise_for_status()
            pr_data = pr_response.json()

            source_branch = pr_data.get("sourceRefName")
            logger.info(f"PR source branch: {source_branch}")

            # Get commit ID of the PR's source branch
            source_commit_id = pr_data.get("lastMergeSourceCommit", {}).get("commitId")
            if source_commit_id:
                logger.info(f"Using PR source commit ID: {source_commit_id}")

            # Get the latest iteration
            iterations_url = f"{base_url}/_apis/git/repositories/{self.repo_id}/pullrequests/{self.pr_id}/iterations"
            logger.info(f"Getting PR iterations from {iterations_url}")

            iter_response = requests.get(
                iterations_url, headers=headers, params={"api-version": "6.0"}
            )
            iter_response.raise_for_status()
            iterations = iter_response.json().get("value", [])

            if not iterations:
                logger.warning("No iterations found for PR")
                return []

            # Get the latest iteration ID
            latest_iteration = max(iterations, key=lambda x: x.get("id", 0))
            iteration_id = latest_iteration.get("id")
            logger.info(f"Latest iteration ID: {iteration_id}")

            # Get changes from that iteration
            logger.info(f"Getting changes from iteration {iteration_id}")
            changes_url = f"{base_url}/_apis/git/repositories/{self.repo_id}/pullrequests/{self.pr_id}/iterations/{iteration_id}/changes"
            changes_response = requests.get(
                changes_url, headers=headers, params={"api-version": "6.0"}
            )
            changes_response.raise_for_status()
            changes_data = changes_response.json()

            # Log the structure of the response for debugging
            logger.info(f"Changes API response structure: {list(changes_data.keys())}")

            # Try to get the changes from different possible keys in the API response
            changes = []
            if "changes" in changes_data:
                changes = changes_data.get("changes", [])
            elif "changeEntries" in changes_data:
                changes = changes_data.get("changeEntries", [])
            else:
                logger.warning(
                    "No 'changes' key in API response, trying alternative formats"
                )
                # Try to find changes in the top-level object
                for key, value in changes_data.items():
                    if isinstance(value, list) and len(value) > 0:
                        if any(
                            isinstance(item, dict) and "item" in item for item in value
                        ):
                            changes = value
                            break

            logger.info(f"Found {len(changes)} changes in alternative format")

            # Process each change
            for change in changes:
                change_type = change.get("changeType", "")

                # Try to find file path in different keys
                file_path = None

                # Check common locations for file path
                if isinstance(change.get("item"), dict):
                    file_path = change["item"].get("path")
                elif "path" in change:
                    file_path = change.get("path")
                elif isinstance(change.get("item"), str):
                    file_path = change["item"]
                elif "sourceServerItem" in change:
                    file_path = change.get("sourceServerItem")
                elif "targetServerItem" in change:
                    file_path = change.get("targetServerItem")

                # If still no path found, try to examine all keys
                if not file_path:
                    for key, value in change.items():
                        if isinstance(value, str) and (
                            value.startswith("/")
                            or value.endswith(".bicep")
                            or value.endswith(".tf")
                            or value.endswith(".json")
                            or value.endswith(".yaml")
                            or value.endswith(".yml")
                        ):
                            file_path = value
                            logger.info(
                                f"Found potential file path in key '{key}': {value}"
                            )
                            break

                if not file_path:
                    logger.warning(f"Could not find file path in change: {change}")
                    continue

                logger.info(f"Processing change of type: {change_type}")

                # Only process changes that are additions or edits
                if change_type.lower() in ("add", "edit") or "objectId" in change.get(
                    "item", {}
                ):
                    try:
                        # Remove leading slash if present for consistency
                        if file_path.startswith("/"):
                            clean_path = file_path[1:]
                        else:
                            clean_path = file_path

                        # Check if this is a relevant file type
                        file_extension = os.path.splitext(clean_path)[1].lower()
                        if file_extension in (
                            ".bicep",
                            ".json",
                            ".tf",
                            ".yaml",
                            ".yml",
                            ".arm",
                            ".ps1",
                            ".sh",
                        ):
                            # Get file content - First try by commit ID if available
                            file_content = None

                            # Try multiple approaches to get the file content
                            # 1. Try using the PR's source commit ID if available
                            if source_commit_id:
                                try:
                                    file_content = self.get_file_content(
                                        repository_id=self.repo_id,
                                        file_path=clean_path,
                                        commit_id=source_commit_id,
                                    )
                                    if file_content:
                                        logger.info(
                                            f"Retrieved content for {clean_path} using source commit ID"
                                        )
                                except Exception as e:
                                    logger.warning(
                                        f"Failed to get content using source commit ID: {str(e)}"
                                    )

                            # 2. If that fails, try using the branch name
                            if not file_content and source_branch:
                                try:
                                    file_content = self.get_file_content(
                                        repository_id=self.repo_id,
                                        file_path=clean_path,
                                        branch_name=source_branch,
                                    )
                                    if file_content:
                                        logger.info(
                                            f"Retrieved content for {clean_path} using source branch"
                                        )
                                except Exception as e:
                                    logger.warning(
                                        f"Failed to get content using source branch: {str(e)}"
                                    )

                            # 3. If change is an "add" type, the file might only exist in the PR's commit
                            if not file_content and "objectId" in change.get(
                                "item", {}
                            ):
                                try:
                                    # Try to get file directly by its object ID
                                    object_id = change["item"]["objectId"]
                                    logger.info(
                                        f"Trying to get content for {clean_path} using object ID: {object_id}"
                                    )

                                    # Use a different API endpoint to get content by object ID
                                    blob_url = f"{base_url}/_apis/git/repositories/{self.repo_id}/blobs/{object_id}"
                                    blob_response = requests.get(
                                        blob_url,
                                        headers=headers,
                                        params={
                                            "api-version": "6.0",
                                            "$format": "text",
                                        },
                                    )

                                    if blob_response.status_code == 200:
                                        file_content = blob_response.text
                                        logger.info(
                                            f"Retrieved content for {clean_path} using object ID"
                                        )
                                except Exception as e:
                                    logger.warning(
                                        f"Failed to get content using object ID: {str(e)}"
                                    )

                            # 4. For new files, try fetching using iteration source commit
                            if not file_content and iteration_id:
                                try:
                                    iter_details = next(
                                        (
                                            i
                                            for i in iterations
                                            if i.get("id") == iteration_id
                                        ),
                                        None,
                                    )
                                    if (
                                        iter_details
                                        and "sourceCommitId" in iter_details
                                    ):
                                        commit_id = iter_details["sourceCommitId"]
                                        logger.info(
                                            f"Trying to get content using iteration source commit: {commit_id}"
                                        )
                                        file_content = self.get_file_content(
                                            repository_id=self.repo_id,
                                            file_path=clean_path,
                                            commit_id=commit_id,
                                        )
                                        if file_content:
                                            logger.info(
                                                f"Retrieved content for {clean_path} using iteration source commit"
                                            )
                                except Exception as e:
                                    logger.warning(
                                        f"Failed to get content using iteration source commit: {str(e)}"
                                    )

                            if file_content:
                                files.append(
                                    {
                                        "path": clean_path,
                                        "content": file_content,
                                        "extension": file_extension,
                                    }
                                )
                                logger.info(f"Added file {clean_path} to analysis list")
                            else:
                                logger.error(
                                    f"Failed to get content for {file_path} after multiple attempts"
                                )
                        else:
                            logger.info(
                                f"Skipping file with unsupported extension: {file_extension}"
                            )
                    except Exception as e:
                        logger.error(f"Error processing change {file_path}: {str(e)}")

            # If no files found from PR changes, try direct repository access
            if not files:
                logger.warning(
                    "No files found from PR changes, trying direct repository access"
                )

                try:
                    # Get all items from the repository
                    # Clean branch name - remove refs/heads/ prefix if it exists
                    if source_branch and source_branch.startswith("refs/heads/"):
                        branch_name_clean = source_branch.replace("refs/heads/", "")
                    else:
                        branch_name_clean = source_branch

                    items_url = (
                        f"{base_url}/_apis/git/repositories/{self.repo_id}/items"
                    )
                    items_params = {
                        "api-version": "6.0",
                        "recursionLevel": "Full",
                    }

                    # Add branch name if available
                    if branch_name_clean:
                        items_params["versionDescriptor.versionType"] = "branch"
                        items_params["versionDescriptor.version"] = branch_name_clean

                    # Otherwise use commit ID if available
                    elif source_commit_id:
                        items_params["versionDescriptor.versionType"] = "commit"
                        items_params["versionDescriptor.version"] = source_commit_id

                    items_response = requests.get(
                        items_url, headers=headers, params=items_params
                    )
                    items_response.raise_for_status()
                    items_data = items_response.json()

                    items = items_data.get("value", [])
                    logger.info(f"Found {len(items)} items in repository")

                    # Filter for files with specific extensions
                    for item in items:
                        item_path = item.get("path", "")
                        if not item.get("isFolder", False) and item_path:
                            file_extension = os.path.splitext(item_path)[1].lower()
                            if file_extension in (
                                ".bicep",
                                ".json",
                                ".tf",
                                ".yaml",
                                ".yml",
                                ".arm",
                                ".ps1",
                                ".sh",
                            ):
                                try:
                                    # Get the file content
                                    file_content = self.get_file_content(
                                        repository_id=self.repo_id,
                                        file_path=item_path,
                                        branch_name=source_branch,
                                    )

                                    if file_content:
                                        # Remove leading slash if present
                                        if item_path.startswith("/"):
                                            clean_path = item_path[1:]
                                        else:
                                            clean_path = item_path

                                        files.append(
                                            {
                                                "path": clean_path,
                                                "content": file_content,
                                                "extension": file_extension,
                                            }
                                        )
                                        logger.info(
                                            f"Added file {clean_path} to analysis list"
                                        )
                                except Exception as e:
                                    logger.error(
                                        f"Error getting content for {item_path}: {str(e)}"
                                    )

                except Exception as e:
                    logger.error(f"Error getting repository items: {str(e)}")

            if not files:
                logger.warning("No files found, falling back to sample files")
                # Create a sample file for testing
                sample_content = """
                resource "azurerm_storage_account" "example" {
                  name                     = "examplestorage"
                  resource_group_name      = azurerm_resource_group.example.name
                  location                 = azurerm_resource_group.example.location
                  account_tier             = "Standard"
                  account_replication_type = "LRS"
                }
                """
                files.append(
                    {"path": "sample.tf", "content": sample_content, "extension": ".tf"}
                )
                logger.info("Created 1 sample files for analysis")

            return files

        except Exception as e:
            logger.error(f"Error getting PR files: {str(e)}")
            return []

    def create_security_branch(self) -> str:
        """Create a new feature branch for security fixes.

        Returns:
            str: The name of the created branch
        """
        try:
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            branch_name = f"iac-guardian-security-fixes-{timestamp}"

            project = os.environ.get("AZURE_DEVOPS_PROJECT")
            if not project:
                raise ValueError("Environment variable AZURE_DEVOPS_PROJECT must be set")

            # Get Git client
            git_client = self._get_git_client()

            # Get repository information
            repo = git_client.get_repository(repository_id=self.repo_id, project=project)

            # Get default branch (usually main or master)
            default_branch = repo.default_branch
            if not default_branch:
                # Fallback to common default branch names
                for branch_name_candidate in ["refs/heads/main", "refs/heads/master"]:
                    try:
                        git_client.get_branch(
                            repository_id=self.repo_id,
                            name=branch_name_candidate.replace("refs/heads/", ""),
                            project=project
                        )
                        default_branch = branch_name_candidate
                        break
                    except:
                        continue

                if not default_branch:
                    raise ValueError("Could not determine default branch")

            logger.info(f"Creating branch '{branch_name}' from '{default_branch}'")

            # Get the latest commit from default branch
            try:
                # Try with GitQueryCommitsCriteria object (newer SDK)
                from azure.devops.v6_0.git.models import GitQueryCommitsCriteria, GitVersionDescriptor

                version_descriptor = GitVersionDescriptor(
                    version=default_branch.replace("refs/heads/", ""),
                    version_type="branch"
                )

                search_criteria = GitQueryCommitsCriteria(
                    item_version=version_descriptor,
                    top=1
                )

                commits = git_client.get_commits(
                    repository_id=self.repo_id,
                    search_criteria=search_criteria,
                    project=project
                )
            except (ImportError, AttributeError):
                # Fallback: try with dictionary (older SDK)
                try:
                    commits = git_client.get_commits(
                        repository_id=self.repo_id,
                        search_criteria={
                            "itemVersion": {
                                "version": default_branch.replace("refs/heads/", ""),
                                "versionType": "branch"
                            },
                            "top": 1
                        },
                        project=project
                    )
                except:
                    # Final fallback: get commits without search criteria and take the first one
                    all_commits = git_client.get_commits(
                        repository_id=self.repo_id,
                        project=project
                    )
                    commits = all_commits[:1] if all_commits else []

            if not commits:
                raise ValueError(f"No commits found in branch {default_branch}")

            # Handle both list and object responses
            if isinstance(commits, list):
                latest_commit = commits[0]
            else:
                # If commits is a response object with a value property
                commit_list = getattr(commits, 'value', commits)
                if isinstance(commit_list, list) and commit_list:
                    latest_commit = commit_list[0]
                else:
                    latest_commit = commits

            # Get commit ID - handle both object and dict formats
            if hasattr(latest_commit, 'commit_id'):
                commit_id = latest_commit.commit_id
            elif isinstance(latest_commit, dict):
                commit_id = latest_commit.get('commitId') or latest_commit.get('commit_id')
            else:
                raise ValueError("Could not extract commit ID from latest commit")

            # Create new branch with retry logic for rate limiting
            new_branch_ref = {
                "name": f"refs/heads/{branch_name}",
                "oldObjectId": "0000000000000000000000000000000000000000",
                "newObjectId": commit_id
            }

            max_retries = 3
            for attempt in range(max_retries):
                try:
                    git_client.update_refs(
                        ref_updates=[new_branch_ref],
                        repository_id=self.repo_id,
                        project=project
                    )
                    break
                except Exception as e:
                    if "rate limit" in str(e).lower() and attempt < max_retries - 1:
                        wait_time = (attempt + 1) * 2  # Exponential backoff
                        logger.warning(f"Rate limit hit, waiting {wait_time} seconds before retry...")
                        time.sleep(wait_time)
                        continue
                    else:
                        raise

            logger.info(f"Successfully created branch: {branch_name}")
            self.created_branch_name = branch_name
            return branch_name

        except Exception as e:
            logger.error(f"Error creating security branch: {str(e)}")
            raise

    def get_repository_files(self) -> List[Dict]:
        """Get all IaC files from the repository.

        Returns:
            List[Dict]: List of file information with path and content
        """
        try:
            project = os.environ.get("AZURE_DEVOPS_PROJECT")
            if not project:
                raise ValueError("Environment variable AZURE_DEVOPS_PROJECT must be set")

            # Get Git client using the same pattern as create_security_branch
            git_client = self._get_git_client()

            # Check repository access permissions first
            try:
                repo = git_client.get_repository(repository_id=self.repo_id, project=project)
                logger.info(f"Repository access confirmed: {repo.name}")
            except Exception as e:
                if "403" in str(e) or "unauthorized" in str(e).lower():
                    raise PermissionError(f"Insufficient permissions to access repository {self.repo_id}. Please check your Azure DevOps PAT permissions.")
                else:
                    raise

            # Get all items from repository with timeout handling
            logger.info("Retrieving repository file structure...")
            try:
                # Try different parameter combinations for different SDK versions
                try:
                    # Try with include_content parameter (newer SDK)
                    items = git_client.get_items(
                        repository_id=self.repo_id,
                        project=project,
                        recursion_level="Full",
                        include_content=False
                    )
                except TypeError:
                    # Fallback: try without include_content parameter (older SDK)
                    items = git_client.get_items(
                        repository_id=self.repo_id,
                        project=project,
                        recursion_level="Full"
                    )
            except Exception as e:
                if "timeout" in str(e).lower():
                    raise TimeoutError("Repository analysis timed out. The repository may be too large for comprehensive analysis. Consider analyzing specific folders instead.")
                else:
                    raise

            # Handle different response formats
            if hasattr(items, 'value'):
                items_list = items.value
            elif isinstance(items, list):
                items_list = items
            else:
                items_list = [items] if items else []

            # Filter for IaC files with size and count limits
            supported_extensions = {".tf", ".bicep", ".json", ".yaml", ".yml", ".ps1", ".sh"}
            iac_files = []
            max_files = 100  # Limit to prevent timeout
            max_file_size = 1024 * 1024  # 1MB limit per file
            processed_files = 0

            logger.info(f"Found {len(items_list)} total items in repository")

            for item in items_list:
                # Handle different item formats (object vs dict)
                if hasattr(item, 'is_folder'):
                    is_folder = item.is_folder
                    file_path = item.path
                    file_size = getattr(item, 'size', None)
                elif isinstance(item, dict):
                    is_folder = item.get('isFolder', False)
                    file_path = item.get('path')
                    file_size = item.get('size')
                else:
                    logger.warning(f"Unknown item format: {type(item)}")
                    continue

                if is_folder:
                    continue

                # Check file count limit
                if processed_files >= max_files:
                    logger.warning(f"Reached maximum file limit ({max_files}). Some files may not be analyzed.")
                    break

                if not file_path:
                    continue

                # Check file extension
                file_ext = os.path.splitext(file_path)[1].lower()
                if file_ext not in supported_extensions:
                    continue

                # Check file size limit
                if file_size and file_size > max_file_size:
                    logger.warning(f"Skipping large file {file_path} ({file_size} bytes > {max_file_size} bytes)")
                    continue

                # Skip certain directories/files
                skip_patterns = [
                    "/.git/", "/node_modules/", "/.vscode/", "/bin/", "/obj/",
                    ".min.", ".bundle.", ".pack.", "/test/", "/tests/", "/.terraform/"
                ]
                if any(pattern in file_path.lower() for pattern in skip_patterns):
                    continue

                # Get file content with retry logic and rate limiting
                max_retries = 3
                file_retrieved = False

                for attempt in range(max_retries):
                    try:
                        # Add small delay to avoid rate limiting
                        if processed_files > 0 and processed_files % 10 == 0:
                            time.sleep(1)  # Brief pause every 10 files

                        # Try different parameter combinations for file content retrieval
                        try:
                            # Try with all parameters (newer SDK)
                            file_content = git_client.get_item_content(
                                repository_id=self.repo_id,
                                path=file_path,
                                project=project,
                                download=False,
                                include_content=True
                            )
                        except TypeError:
                            try:
                                # Try without include_content parameter
                                file_content = git_client.get_item_content(
                                    repository_id=self.repo_id,
                                    path=file_path,
                                    project=project,
                                    download=False
                                )
                            except TypeError:
                                # Final fallback: minimal parameters
                                file_content = git_client.get_item_content(
                                    repository_id=self.repo_id,
                                    path=file_path,
                                    project=project
                                )

                        if file_content:
                            # Decode content if it's bytes
                            if isinstance(file_content, bytes):
                                try:
                                    content_str = file_content.decode('utf-8')
                                except UnicodeDecodeError:
                                    logger.warning(f"Could not decode file {file_path}, skipping")
                                    break
                            else:
                                content_str = str(file_content)

                            # Skip empty files
                            if not content_str.strip():
                                logger.info(f"Skipping empty file: {file_path}")
                                break

                            iac_files.append({
                                "path": file_path,
                                "content": content_str
                            })

                            logger.info(f"Added file for analysis: {file_path}")
                            file_retrieved = True
                            processed_files += 1
                            break

                    except Exception as e:
                        if "rate limit" in str(e).lower() and attempt < max_retries - 1:
                            wait_time = (attempt + 1) * 2
                            logger.warning(f"Rate limit hit for {file_path}, waiting {wait_time} seconds...")
                            time.sleep(wait_time)
                            continue
                        elif attempt == max_retries - 1:
                            logger.warning(f"Could not get content for {file_path} after {max_retries} attempts: {str(e)}")
                        break

                if not file_retrieved:
                    continue

            logger.info(f"Found {len(iac_files)} IaC files in repository")
            return iac_files

        except Exception as e:
            logger.error(f"Error getting repository files: {str(e)}")
            raise

    def run(self) -> None:
        """Run the security PR review workflow."""
        try:
            if self.mode == "create_pr":
                # Enhanced workflow for creating PR with security recommendations
                logger.info("Step 1: Preparing Azure Security Benchmark")
                self.prepare_benchmark()

                logger.info("Step 2: Creating security fixes branch")
                branch_name = self.create_security_branch()

                logger.info("Step 3: Getting all repository files")
                files = self.get_repository_files()

                logger.info("Step 4: Analyzing repository files")
                findings = self.analyze_files(files)

                logger.info("Step 5: Creating pull request with security recommendations")
                pr_id = self.create_security_pr(findings, branch_name)

                logger.info("Step 6: Adding detailed PR comments")
                # Temporarily set pr_id for comment posting
                self.pr_id = pr_id
                self.post_enhanced_pr_comments(findings)

                logger.info(f"Security PR creation completed successfully. PR ID: {pr_id}")

            else:
                # Original workflow for existing PR review
                logger.info("Step 1: Preparing Azure Security Benchmark")
                self.prepare_benchmark()

                logger.info("Step 2: Getting files from PR")
                files = self.get_pr_files()

                logger.info("Step 3: Analyzing files")
                findings = self.analyze_files(files)

                logger.info("Step 4: Posting PR comments")
                self.post_pr_comments(findings)

                logger.info("Security PR review completed successfully")

        except Exception as e:
            logger.error(f"Error running security PR review: {e}")
            raise

    def create_security_pr(self, findings: List[Dict], branch_name: str) -> int:
        """Create a pull request with security recommendations.

        Args:
            findings: List of security findings
            branch_name: Name of the source branch

        Returns:
            int: The ID of the created pull request
        """
        try:
            project = os.environ.get("AZURE_DEVOPS_PROJECT")
            if not project:
                raise ValueError("Environment variable AZURE_DEVOPS_PROJECT must be set")

            git_client = self._get_git_client()

            # Get repository information
            repo = git_client.get_repository(repository_id=self.repo_id, project=project)
            default_branch = repo.default_branch or "refs/heads/main"

            # Clean branch names
            source_branch = f"refs/heads/{branch_name}"
            target_branch = default_branch

            # Generate PR title and description
            current_date = datetime.datetime.now().strftime("%Y-%m-%d")
            pr_title = f"IaC Guardian Security Recommendations - {current_date}"

            # Create comprehensive PR description
            pr_description = self._generate_pr_description(findings)

            # Create pull request
            logger.info(f"Creating PR: {pr_title}")
            logger.info(f"Source: {source_branch} -> Target: {target_branch}")

            try:
                # Try with GitPullRequest object (newer SDK)
                from azure.devops.v6_0.git.models import GitPullRequest

                pr_object = GitPullRequest(
                    source_ref_name=source_branch,
                    target_ref_name=target_branch,
                    title=pr_title,
                    description=pr_description,
                    is_draft=False
                )

                created_pr = git_client.create_pull_request(
                    git_pull_request_to_create=pr_object,
                    repository_id=self.repo_id,
                    project=project
                )
            except (ImportError, AttributeError):
                # Fallback: try with dictionary (older SDK)
                pr_data = {
                    "sourceRefName": source_branch,
                    "targetRefName": target_branch,
                    "title": pr_title,
                    "description": pr_description,
                    "isDraft": False
                }

                created_pr = git_client.create_pull_request(
                    git_pull_request_to_create=pr_data,
                    repository_id=self.repo_id,
                    project=project
                )

            # Extract PR ID - handle both object and dict formats
            if hasattr(created_pr, 'pull_request_id'):
                pr_id = created_pr.pull_request_id
            elif isinstance(created_pr, dict):
                pr_id = created_pr.get('pullRequestId') or created_pr.get('pull_request_id')
            else:
                raise ValueError("Could not extract PR ID from created pull request")
            self.created_pr_id = pr_id

            logger.info(f"Successfully created PR with ID: {pr_id}")
            return pr_id

        except Exception as e:
            logger.error(f"Error creating security PR: {str(e)}")
            raise

    def _generate_pr_description(self, findings: List[Dict]) -> str:
        """Generate comprehensive PR description with security findings summary.

        Args:
            findings: List of security findings

        Returns:
            str: Formatted PR description
        """
        if not findings:
            return """# IaC Guardian Security Analysis

✅ **No security issues found!**

Your Infrastructure-as-Code follows security best practices according to the Azure Security Benchmark v3.0.

## Analysis Details
- **Analysis Date**: {date}
- **Files Analyzed**: Repository-wide scan
- **Security Framework**: Azure Security Benchmark v3.0
- **Analysis Method**: Threat actor-centric evaluation

## Next Steps
This PR was created to document the security analysis. You may close it if no changes are needed.
""".format(date=datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"))

        # Count findings by severity
        severity_counts = {}
        domain_counts = {"Identity Management": 0, "Network Security": 0, "Data Protection": 0, "Other": 0}

        for finding in findings:
            severity = finding.get("severity", "UNKNOWN").upper()
            severity_counts[severity] = severity_counts.get(severity, 0) + 1

            # Count by domain based on control ID
            control_id = finding.get("control_id", "")
            if control_id.startswith("IM-"):
                domain_counts["Identity Management"] += 1
            elif control_id.startswith("NS-"):
                domain_counts["Network Security"] += 1
            elif control_id.startswith("DP-"):
                domain_counts["Data Protection"] += 1
            else:
                domain_counts["Other"] += 1

        # Generate severity summary
        severity_summary = []
        severity_order = ["CRITICAL", "HIGH", "MEDIUM", "LOW", "INFO"]
        severity_icons = {"CRITICAL": "🔴", "HIGH": "🟠", "MEDIUM": "🟡", "LOW": "🔵", "INFO": "ℹ️"}

        for severity in severity_order:
            count = severity_counts.get(severity, 0)
            if count > 0:
                icon = severity_icons.get(severity, "•")
                severity_summary.append(f"- {icon} **{severity}**: {count} finding{'s' if count != 1 else ''}")

        # Generate domain summary
        domain_summary = []
        for domain, count in domain_counts.items():
            if count > 0:
                domain_summary.append(f"- **{domain}**: {count} finding{'s' if count != 1 else ''}")

        # Generate top findings preview
        top_findings = []
        critical_high = [f for f in findings if f.get("severity", "").upper() in ["CRITICAL", "HIGH"]]
        preview_findings = critical_high[:3] if critical_high else findings[:3]

        for i, finding in enumerate(preview_findings, 1):
            severity = finding.get("severity", "UNKNOWN").upper()
            control_id = finding.get("control_id", "N/A")
            description = finding.get("description", "No description available")
            file_path = finding.get("file_path", "Unknown file")

            # Truncate long descriptions
            if len(description) > 100:
                description = description[:97] + "..."

            icon = severity_icons.get(severity, "•")
            top_findings.append(f"{i}. {icon} **{control_id}** - {description}\n   📁 `{file_path}`")

        description = f"""# IaC Guardian Security Analysis Report

🛡️ **Security Analysis Completed** - Found **{len(findings)}** security finding{'s' if len(findings) != 1 else ''} requiring attention.

## 📊 Summary by Severity
{chr(10).join(severity_summary)}

## 🎯 Domain Priority Breakdown
{chr(10).join(domain_summary)}

## 🔍 Top Priority Findings
{chr(10).join(top_findings)}

{'...' if len(findings) > 3 else ''}

## 📋 Analysis Details
- **Analysis Date**: {date}
- **Files Analyzed**: Repository-wide scan
- **Security Framework**: Azure Security Benchmark v3.0
- **Analysis Method**: Threat actor-centric evaluation
- **Domain Priority**: Identity → Network → Data Protection

## 🚀 Next Steps
1. Review individual security findings in the PR comments below
2. Implement recommended fixes for critical and high-severity issues
3. Consider the threat actor perspective explanations for context
4. Validate changes against deployment worthiness scores

## 📄 Detailed Report
A comprehensive HTML report with interactive visualizations has been generated and will be available in the PR comments.

---
*This PR was automatically created by IaC Guardian to help improve your infrastructure security posture.*
""".format(
            date=datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        )

        return description

    def post_enhanced_pr_comments(self, findings: List[Dict]) -> None:
        """Post enhanced structured comments to the pull request.

        Args:
            findings: List of security findings to post as comments
        """
        if not findings:
            logger.info("No findings to post as PR comments")
            return

        try:
            project = os.environ.get("AZURE_DEVOPS_PROJECT")
            if not project:
                raise ValueError("Environment variable AZURE_DEVOPS_PROJECT must be set")

            git_client = self._get_git_client()

            logger.info(f"Posting enhanced comments for {len(findings)} security findings")

            # Group findings by file for better organization
            findings_by_file = {}
            for finding in findings:
                file_path = finding.get("file_path", "unknown")
                if file_path not in findings_by_file:
                    findings_by_file[file_path] = []
                findings_by_file[file_path].append(finding)

            # Post individual comments for each finding
            for file_path, file_findings in findings_by_file.items():
                for finding in file_findings:
                    try:
                        comment_content = self._format_enhanced_finding_comment(finding)

                        # Create thread with file context if possible
                        thread_data = {
                            "comments": [{
                                "content": comment_content,
                                "parentCommentId": 0,
                                "commentType": "text"
                            }],
                            "status": "active"
                        }

                        # Add file context if available
                        if file_path and file_path != "unknown":
                            thread_data["threadContext"] = {"filePath": file_path}

                            # Add line number context if available
                            line_num = finding.get("line", finding.get("line_number"))
                            if line_num and str(line_num).isdigit():
                                thread_data["threadContext"]["rightFileStart"] = int(line_num)
                                thread_data["threadContext"]["rightFileEnd"] = int(line_num)

                        git_client.create_thread(
                            comment_thread=thread_data,
                            repository_id=self.repo_id,
                            pull_request_id=self.pr_id,
                            project=project
                        )

                        logger.info(f"Posted enhanced comment for {finding.get('control_id', 'N/A')} in {file_path}")

                    except Exception as e:
                        logger.error(f"Error posting comment for finding in {file_path}: {str(e)}")
                        continue

            # Post comprehensive summary comment
            try:
                summary_comment = self._format_enhanced_summary_comment(findings)

                summary_thread = {
                    "comments": [{
                        "content": summary_comment,
                        "parentCommentId": 0,
                        "commentType": "text"
                    }],
                    "status": "active"
                }

                git_client.create_thread(
                    comment_thread=summary_thread,
                    repository_id=self.repo_id,
                    pull_request_id=self.pr_id,
                    project=project
                )

                logger.info("Posted enhanced security summary comment")

            except Exception as e:
                logger.error(f"Error posting summary comment: {str(e)}")

            # Generate and reference HTML report
            try:
                self._generate_and_reference_html_report(findings)
            except Exception as e:
                logger.error(f"Error generating HTML report reference: {str(e)}")

            logger.info(f"Successfully posted enhanced comments for {len(findings)} findings across {len(findings_by_file)} files")

        except Exception as e:
            logger.error(f"Error posting enhanced PR comments: {str(e)}")
            logger.exception(e)

    def _format_enhanced_finding_comment(self, finding: Dict) -> str:
        """Format an individual security finding as an enhanced PR comment.

        Args:
            finding: Security finding dictionary

        Returns:
            str: Formatted comment content
        """
        control_id = finding.get("control_id", "N/A")
        severity = finding.get("severity", "UNKNOWN").upper()
        file_path = finding.get("file_path", "Unknown file")
        line_num = finding.get("line", finding.get("line_number", "N/A"))
        description = finding.get("description", "No description available")
        remediation = finding.get("remediation", "No remediation guidance available")

        # Get deployment worthiness score if available
        score = finding.get("deployment_worthiness_score", finding.get("score", "N/A"))

        # Severity icons and colors
        severity_icons = {
            "CRITICAL": "🔴",
            "HIGH": "🟠",
            "MEDIUM": "🟡",
            "LOW": "🔵",
            "INFO": "ℹ️"
        }

        severity_icon = severity_icons.get(severity, "•")

        # Generate threat actor perspective explanation
        threat_perspective = self._generate_threat_actor_explanation(finding)

        # Format code example if remediation includes code
        remediation_formatted = self._format_remediation_with_code(remediation)

        comment = f"""# {severity_icon} Security Finding: {control_id}

## 📋 Finding Details
- **Severity**: {severity_icon} **{severity}**
- **Control ID**: `{control_id}` (Azure Security Benchmark v3.0)
- **File**: `{file_path}`
- **Line**: {line_num}
- **Deployment Worthiness Score**: {score}/100

## 🔍 Security Issue
{description}

## 🎯 Threat Actor Perspective
{threat_perspective}

## 🛠️ Recommended Fix
{remediation_formatted}

---
*This finding was identified using IaC Guardian's threat actor-centric analysis methodology.*
"""

        return comment

    def _generate_threat_actor_explanation(self, finding: Dict) -> str:
        """Generate threat actor perspective explanation for a finding.

        Args:
            finding: Security finding dictionary

        Returns:
            str: Threat actor perspective explanation
        """
        control_id = finding.get("control_id", "")
        severity = finding.get("severity", "").upper()

        # Generate context-aware threat explanations based on control type
        if control_id.startswith("NS-"):
            return """**Network Attack Vector**: An attacker could exploit this network configuration to:
- Gain unauthorized access to resources
- Perform lateral movement within the infrastructure
- Intercept or manipulate network traffic
- Establish persistent access points

**Blast Radius**: Network misconfigurations often provide the broadest attack surface, potentially affecting multiple resources and enabling privilege escalation."""

        elif control_id.startswith("IM-"):
            return """**Identity Attack Vector**: An attacker could exploit this identity configuration to:
- Escalate privileges beyond intended scope
- Impersonate legitimate users or services
- Bypass authentication mechanisms
- Gain persistent access to sensitive resources

**Blast Radius**: Identity vulnerabilities can provide attackers with the keys to the kingdom, enabling access to multiple systems and data stores."""

        elif control_id.startswith("DP-"):
            return """**Data Attack Vector**: An attacker could exploit this data protection gap to:
- Access sensitive data without proper authorization
- Exfiltrate confidential information
- Manipulate or corrupt critical data
- Bypass data loss prevention controls

**Blast Radius**: Data protection failures can lead to compliance violations, intellectual property theft, and significant business impact."""

        else:
            # Generic threat explanation
            if severity in ["CRITICAL", "HIGH"]:
                return """**Attack Vector**: This security gap provides attackers with opportunities to:
- Compromise system integrity and availability
- Gain unauthorized access to resources
- Establish footholds for further exploitation
- Cause significant business disruption

**Blast Radius**: High-severity findings typically indicate vulnerabilities that could lead to complete system compromise."""
            else:
                return """**Attack Vector**: While not immediately critical, this configuration could be exploited by attackers to:
- Gather reconnaissance information
- Establish minor footholds in the system
- Combine with other vulnerabilities for greater impact
- Gradually escalate privileges over time

**Blast Radius**: Lower-severity findings contribute to overall attack surface and should be addressed as part of defense-in-depth strategy."""

    def _format_remediation_with_code(self, remediation: str) -> str:
        """Format remediation guidance with proper code formatting.

        Args:
            remediation: Raw remediation text

        Returns:
            str: Formatted remediation with code blocks
        """
        # Simple heuristic to detect code snippets and format them
        lines = remediation.split('\n')
        formatted_lines = []
        in_code_block = False

        for line in lines:
            # Detect JSON-like content
            if ('{' in line and '}' in line) or line.strip().startswith('"') or line.strip().endswith('",'):
                if not in_code_block:
                    formatted_lines.append('```json')
                    in_code_block = True
                formatted_lines.append(line)
            # Detect other code patterns
            elif any(keyword in line.lower() for keyword in ['bicep', 'terraform', 'arm template', 'resource']):
                if in_code_block:
                    formatted_lines.append('```')
                    in_code_block = False
                formatted_lines.append(line)
            else:
                if in_code_block:
                    formatted_lines.append('```')
                    in_code_block = False
                formatted_lines.append(line)

        if in_code_block:
            formatted_lines.append('```')

        return '\n'.join(formatted_lines)

    def _format_enhanced_summary_comment(self, findings: List[Dict]) -> str:
        """Format comprehensive summary comment for PR.

        Args:
            findings: List of all security findings

        Returns:
            str: Formatted summary comment
        """
        if not findings:
            return """# 🛡️ IaC Guardian Security Analysis Summary

✅ **No security issues found!**

Your Infrastructure-as-Code follows security best practices according to the Azure Security Benchmark v3.0.

## 📊 Analysis Results
- **Total Files Analyzed**: Repository-wide scan
- **Security Framework**: Azure Security Benchmark v3.0
- **Analysis Method**: Threat actor-centric evaluation
- **Status**: ✅ PASSED

## 🎯 Security Posture
Your infrastructure demonstrates strong security practices with no identified vulnerabilities.

---
*Analysis completed by IaC Guardian - Infrastructure Security Analysis Tool*
"""

        # Calculate statistics
        total_findings = len(findings)
        severity_counts = {}
        domain_counts = {"Identity Management": 0, "Network Security": 0, "Data Protection": 0, "Other": 0}
        files_affected = set()

        for finding in findings:
            # Count by severity
            severity = finding.get("severity", "UNKNOWN").upper()
            severity_counts[severity] = severity_counts.get(severity, 0) + 1

            # Count by domain
            control_id = finding.get("control_id", "")
            if control_id.startswith("IM-"):
                domain_counts["Identity Management"] += 1
            elif control_id.startswith("NS-"):
                domain_counts["Network Security"] += 1
            elif control_id.startswith("DP-"):
                domain_counts["Data Protection"] += 1
            else:
                domain_counts["Other"] += 1

            # Track affected files
            file_path = finding.get("file_path")
            if file_path:
                files_affected.add(file_path)

        # Generate severity breakdown
        severity_order = ["CRITICAL", "HIGH", "MEDIUM", "LOW", "INFO"]
        severity_icons = {"CRITICAL": "🔴", "HIGH": "🟠", "MEDIUM": "🟡", "LOW": "🔵", "INFO": "ℹ️"}

        severity_breakdown = []
        for severity in severity_order:
            count = severity_counts.get(severity, 0)
            if count > 0:
                icon = severity_icons.get(severity, "•")
                severity_breakdown.append(f"- {icon} **{severity}**: {count}")

        # Generate domain breakdown
        domain_breakdown = []
        for domain, count in domain_counts.items():
            if count > 0:
                domain_breakdown.append(f"- **{domain}**: {count}")

        # Determine overall security posture
        critical_high_count = severity_counts.get("CRITICAL", 0) + severity_counts.get("HIGH", 0)
        if critical_high_count == 0:
            posture_status = "🟢 GOOD"
            posture_desc = "No critical or high-severity issues found. Address medium and low-severity findings to improve security posture."
        elif critical_high_count <= 3:
            posture_status = "🟡 NEEDS ATTENTION"
            posture_desc = "Some critical or high-severity issues require immediate attention."
        else:
            posture_status = "🔴 REQUIRES IMMEDIATE ACTION"
            posture_desc = "Multiple critical or high-severity issues pose significant security risks."

        summary = f"""# 🛡️ IaC Guardian Security Analysis Summary

## 📊 Analysis Results
- **Total Findings**: {total_findings}
- **Files Affected**: {len(files_affected)}
- **Security Framework**: Azure Security Benchmark v3.0
- **Analysis Method**: Threat actor-centric evaluation

## 🎯 Severity Breakdown
{chr(10).join(severity_breakdown)}

## 🏗️ Domain Priority Analysis
{chr(10).join(domain_breakdown)}

*Domain Priority Order: Identity → Network → Data Protection*

## 🔍 Overall Security Posture
**Status**: {posture_status}

{posture_desc}

## 📋 Next Steps
1. **Immediate**: Address all CRITICAL and HIGH severity findings
2. **Short-term**: Review and fix MEDIUM severity issues
3. **Long-term**: Consider LOW severity recommendations for defense-in-depth
4. **Validation**: Re-run analysis after implementing fixes

## 📄 Detailed Analysis
- Individual findings are commented on specific files and lines above
- Each finding includes threat actor perspective and remediation guidance
- HTML report with interactive visualizations available in artifacts

## 🔗 Additional Resources
- [Azure Security Benchmark v3.0](https://docs.microsoft.com/en-us/security/benchmark/azure/)
- [IaC Guardian Documentation](https://github.com/your-org/iac-guardian)

---
*Analysis completed by IaC Guardian - Infrastructure Security Analysis Tool*
*Report generated on: {datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")}*
"""

        return summary

    def _generate_and_reference_html_report(self, findings: List[Dict]) -> None:
        """Generate HTML report and add reference comment.

        Args:
            findings: List of security findings
        """
        try:
            # Generate HTML report using existing functionality
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            report_dir = Path("reports/security_findings")
            report_dir.mkdir(parents=True, exist_ok=True)

            report_path = report_dir / f"iac_guardian_pr_{self.pr_id}_{timestamp}.html"

            # Use existing HTML export functionality
            if hasattr(self, '_export_findings_to_html'):
                self._export_findings_to_html(findings, str(report_path))

                # Post comment with report reference
                report_comment = f"""# 📊 Interactive Security Report Generated

A comprehensive HTML report with interactive visualizations has been generated for this security analysis.

## 📄 Report Details
- **Report File**: `{report_path.name}`
- **Location**: `{report_path}`
- **Format**: Interactive HTML with Glass UI
- **Features**:
  - Interactive charts and graphs
  - Filterable findings by severity and domain
  - Code snippets with line highlighting
  - Export capabilities (JSON, CSV)

## 🎯 Report Highlights
- Responsive design optimized for all screen sizes
- Domain priority visualization (Identity → Network → Data Protection)
- Threat actor perspective analysis
- Deployment worthiness scoring

## 📁 Accessing the Report
The report has been saved to the repository's reports directory and can be:
1. Downloaded as a build artifact
2. Viewed locally by opening the HTML file
3. Shared with leadership for security posture review

---
*This report complements the individual PR comments with comprehensive visualizations and analysis.*
"""

                try:
                    project = os.environ.get("AZURE_DEVOPS_PROJECT")
                    git_client = self._get_git_client()

                    report_thread = {
                        "comments": [{
                            "content": report_comment,
                            "parentCommentId": 0,
                            "commentType": "text"
                        }],
                        "status": "active"
                    }

                    git_client.create_thread(
                        comment_thread=report_thread,
                        repository_id=self.repo_id,
                        pull_request_id=self.pr_id,
                        project=project
                    )

                    logger.info(f"Posted HTML report reference comment")

                except Exception as e:
                    logger.error(f"Error posting report reference comment: {str(e)}")

            else:
                logger.warning("HTML export functionality not available")

        except Exception as e:
            logger.error(f"Error generating HTML report reference: {str(e)}")


def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description="Review an Azure DevOps PR for security issues or create new PR with security recommendations",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Analyze existing PR
  python security_pr_review.py --repo-id "12345678-90ab-cdef-1234-567890abcdef" --pr-id 123

  # Create new PR with security recommendations
  python security_pr_review.py --repo-id "12345678-90ab-cdef-1234-567890abcdef" --create-pr
        """
    )

    parser.add_argument("--repo-id", required=True, help="Repository ID")

    # Make pr-id and create-pr mutually exclusive
    group = parser.add_mutually_exclusive_group(required=True)
    group.add_argument("--pr-id", type=int, help="Pull Request ID (for analyzing existing PR)")
    group.add_argument("--create-pr", action="store_true",
                      help="Create new PR with security recommendations (repository-wide analysis)")

    return parser.parse_args()


if __name__ == "__main__":
    # Load environment variables
    load_dotenv()

    args = parse_arguments()

    try:
        if args.create_pr:
            # Create new PR with security recommendations
            logger.info(f"Creating security recommendations PR for repository: {args.repo_id}")
            reviewer = SecurityPRReviewer(repo_id=args.repo_id, create_pr=True)
        else:
            # Analyze existing PR
            logger.info(f"Analyzing existing PR {args.pr_id} in repository: {args.repo_id}")
            reviewer = SecurityPRReviewer(repo_id=args.repo_id, pr_id=args.pr_id)

        reviewer.run()

        # Print success message with details
        if args.create_pr and hasattr(reviewer, 'created_pr_id'):
            print(f"\n✅ Successfully created security recommendations PR!")
            print(f"📋 PR ID: {reviewer.created_pr_id}")
            print(f"🌿 Branch: {reviewer.created_branch_name}")
            print(f"🔗 Repository: {args.repo_id}")
        elif not args.create_pr:
            print(f"\n✅ Successfully analyzed PR {args.pr_id}")
            print(f"🔗 Repository: {args.repo_id}")

    except Exception as e:
        logger.error(f"Error in security PR review: {str(e)}")
        print(f"\n❌ Error: {str(e)}")
        print("Please check the logs for more details.")
        sys.exit(1)
